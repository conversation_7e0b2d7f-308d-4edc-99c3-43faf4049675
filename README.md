# 智能AI Agent项目

一个基于AI的智能任务管理系统，支持语音输入、自动分类和提醒功能。

## 功能特性

- 🎤 **语音输入**: 支持语音转文字，便捷输入
- 🤖 **AI智能分类**: 自动识别并分类任务（运动、健康、工作等8个类别）
- ⏰ **智能时间解析**: 识别"明天下午3点"等自然语言时间表达
- 📱 **跨平台应用**: 支持iOS、Android、Web端
- 🔔 **智能提醒**: 基于时间的自动提醒功能
- 📊 **统一管理**: 一个界面管理所有类型的任务和项目

## 技术栈

### 后端
- **框架**: FastAPI
- **数据库**: PostgreSQL
- **缓存**: Redis
- **AI模型**: 通义千问API
- **ORM**: SQLAlchemy

### 前端
- **框架**: Flutter
- **状态管理**: Provider
- **网络请求**: Dio
- **语音识别**: speech_to_text

## 快速开始

### 环境要求
- Python 3.11+
- Flutter 3.8+
- PostgreSQL
- Redis

### 安装运行

1. **克隆项目**
```bash
git clone <repository>
cd betterAi
```

2. **数据库配置**
- 确保PostgreSQL运行，用户: `betterplan`, 密码: `plan123456`
- 数据库会自动创建和初始化

3. **一键启动**
```bash
./start.sh
```

4. **手动启动**

后端:
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python init_db.py  # 首次运行
python main.py
```

前端:
```bash
cd flutter_app
flutter pub get
flutter run -d chrome --web-port 3000  # Web端
flutter run  # 移动端
```

## API接口

- **AI分析**: `POST /api/ai/analyze`
- **项目管理**: `GET/POST /api/items`
- **提醒创建**: `POST /api/items/{id}/reminders`
- **API文档**: http://localhost:8000/docs

## 使用示例

1. 在应用中输入或说出: "明天下午3点打羽毛球"
2. AI自动分析:
   - 分类: 运动
   - 时间: 明天15:00
   - 创建提醒: 明天下午3点提醒打羽毛球
3. 任务自动添加到项目列表

## 项目结构

```
betterAi/
├── backend/          # Python后端
│   ├── app/
│   │   ├── api/      # API路由
│   │   ├── models/   # 数据模型
│   │   ├── services/ # 业务逻辑
│   │   └── ai/       # AI集成
│   └── main.py       # 启动文件
├── flutter_app/      # Flutter前端
│   ├── lib/
│   │   ├── screens/  # 页面
│   │   ├── services/ # API服务
│   │   └── widgets/  # 组件
│   └── pubspec.yaml
└── start.sh          # 启动脚本
```

## 开发说明

### AI模型配置
项目使用通义千问API，需要配置API密钥:
```bash
# backend/.env
QIANWEN_API_KEY=your-api-key
```

### 移动端权限
iOS和Android需要以下权限:
- 麦克风权限（语音输入）
- 网络权限（API调用）
- 本地通知权限（提醒功能）

### 数据库模型
- `User`: 用户信息
- `Category`: 任务分类
- `Item`: 通用项目/任务
- `Reminder`: 提醒设置

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License