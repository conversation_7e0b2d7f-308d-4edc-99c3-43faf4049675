from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, items, ai, conversation, task_steps
from app.core.config import settings

app = FastAPI(
    title="智能AI Agent",
    description="智能分类和提醒系统 - 支持对话式交互",
    version="2.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(items.router, prefix="/api/items", tags=["items"])
app.include_router(ai.router, prefix="/api/ai", tags=["ai"])
app.include_router(conversation.router, prefix="/api/conversation", tags=["conversation"])
app.include_router(task_steps.router, prefix="/api/tasks", tags=["task_steps"])

@app.get("/")
async def root():
    return {
        "message": "智能AI Agent API服务 v2.0", 
        "features": [
            "对话式AI交互",
            "智能任务创建",
            "项目步骤分解", 
            "提醒管理",
            "任务状态跟踪"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)