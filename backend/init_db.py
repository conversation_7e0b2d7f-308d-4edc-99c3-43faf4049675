"""数据库初始化脚本"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.models import Base, Category, User
from app.core.config import settings
import hashlib

# 创建数据库引擎
engine = create_engine(settings.DATABASE_URL)

def init_db():
    """初始化数据库表"""
    Base.metadata.create_all(bind=engine)
    
def seed_data():
    """插入初始数据"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 检查是否已有数据
        if db.query(Category).first():
            print("数据库已有数据，跳过初始化")
            return
        
        # 创建默认分类
        categories = [
            {"name": "运动", "icon": "🏃", "color": "#FF6B6B", "description": "运动健身相关活动"},
            {"name": "健康", "icon": "🏥", "color": "#4ECDC4", "description": "健康医疗相关事项"},
            {"name": "工作", "icon": "💼", "color": "#45B7D1", "description": "工作任务和项目"},
            {"name": "学习", "icon": "📚", "color": "#96CEB4", "description": "学习进修相关"},
            {"name": "生活", "icon": "🏠", "color": "#FECA57", "description": "日常生活事务"},
            {"name": "娱乐", "icon": "🎮", "color": "#FF9FF3", "description": "娱乐休闲活动"},
            {"name": "购物", "icon": "🛒", "color": "#54A0FF", "description": "购物消费相关"},
            {"name": "会议", "icon": "👥", "color": "#5F27CD", "description": "会议讨论事项"}
        ]
        
        for cat_data in categories:
            category = Category(**cat_data)
            db.add(category)
        
        # 创建测试用户
        test_user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password=hashlib.sha256("testpass".encode()).hexdigest(),
            is_active=True
        )
        db.add(test_user)
        
        db.commit()
        print("数据库初始化完成")
        
    except Exception as e:
        db.rollback()
        print(f"数据库初始化失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("初始化数据库...")
    init_db()
    seed_data()