#!/usr/bin/env python3

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine
from app.models.enhanced_models import Base, User, Category, Item, TaskStep, Reminder
from app.models.conversation_models import ConversationContext, AIInteraction, TaskTemplate, ReminderSchedule

def init_database():
    """初始化数据库，创建所有表"""
    print("正在初始化数据库...")
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    print("数据库初始化完成！")
    print("\n创建的表包括:")
    
    # 显示所有表的信息
    from sqlalchemy import inspect
    inspector = inspect(engine)
    
    for table_name in inspector.get_table_names():
        print(f"\n表: {table_name}")
        columns = inspector.get_columns(table_name)
        for column in columns:
            print(f"  - {column['name']}: {column['type']}")

if __name__ == "__main__":
    init_database()