from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.models import schemas
from app.models.enhanced_models import Conversation, Item, Category, Reminder

router = APIRouter()

@router.post("/", response_model=schemas.Item)
async def create_item(
    item: schemas.ItemCreate,
    db: Session = Depends(get_db),
    current_user_id: int = 1  # 临时写死，后续添加认证
):
    """创建新项目"""
    db_item = Item(**item.dict(), user_id=current_user_id)
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item

@router.get("/")
async def get_items(
    skip: int = 0,
    limit: int = 100,
    category_id: int = None,
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """获取用户的所有项目，包含对话ID信息"""
    try:
        query = db.query(Item).filter(Item.user_id == current_user_id)
        
        if category_id:
            query = query.filter(Item.category_id == category_id)
        
        # 置顶的项目排在前面
        items = query.order_by(
            Item.is_pinned.desc(),  # 置顶的在前
            Item.created_at.desc()  # 然后按创建时间倒序
        ).offset(skip).limit(limit).all()
        
        # 为每个项目添加对话ID信息
        result = []
        for item in items:
            # 查找创建此项目的对话
            conversation = db.query(Conversation).filter(
                Conversation.result_item_id == item.id
            ).first()
            
            # 转换为字典并添加对话ID
            # 安全地获取枚举值，兼容大小写
            def get_enum_value(enum_obj):
                if enum_obj is None:
                    return None
                if hasattr(enum_obj, 'value'):
                    return enum_obj.value
                # 如果是字符串，直接返回
                return str(enum_obj)
            
            item_dict = {
                "id": item.id,
                "title": item.title,
                "description": item.description,
                "content": item.content,
                "category_id": item.category_id,
                "user_id": item.user_id,
                "complexity": get_enum_value(item.complexity),
                "status": get_enum_value(item.status),
                "priority": item.priority,
                "ai_confidence": item.ai_confidence,
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "updated_at": item.updated_at.isoformat() if item.updated_at else None,
                "due_date": item.due_date.isoformat() if item.due_date else None,
                "conversation_id": conversation.id if conversation else None,
                "is_pinned": item.is_pinned if hasattr(item, 'is_pinned') else False,
            }
            result.append(item_dict)
        
        return result
    except Exception as e:
        print(f"Error in get_items: {e}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.put("/{item_id}/pin")
async def toggle_item_pin(
    item_id: int,
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """切换任务置顶状态"""
    try:
        # 查找任务
        item = db.query(Item).filter(
            Item.id == item_id,
            Item.user_id == current_user_id
        ).first()
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 切换置顶状态
        item.is_pinned = not getattr(item, 'is_pinned', False)
        db.commit()
        
        return {"success": True, "is_pinned": item.is_pinned}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换置顶状态失败: {str(e)}"
        )

@router.post("/{item_id}/reminders", response_model=schemas.Reminder)
async def create_reminder(
    item_id: int,
    reminder: schemas.ReminderCreate,
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """为项目创建提醒"""
    # 验证项目是否存在且属于当前用户
    item = db.query(Item).filter(
        Item.id == item_id,
        Item.user_id == current_user_id
    ).first()
    
    if not item:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    db_reminder = Reminder(
        **reminder.dict(),
        user_id=current_user_id
    )
    db.add(db_reminder)
    db.commit()
    db.refresh(db_reminder)
    return db_reminder

@router.delete("/{item_id}")
async def delete_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """删除任务及其关联的对话"""
    try:
        print(f"开始删除任务 ID: {item_id}")
        
        # 查找任务
        item = db.query(Item).filter(
            Item.id == item_id,
            Item.user_id == current_user_id
        ).first()
        if not item:
            print(f"任务 {item_id} 不存在")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        print(f"找到任务: {item.title}")
        
        # 查找并删除关联的对话及其消息
        try:
            conversation = db.query(Conversation).filter(
                Conversation.result_item_id == item_id
            ).first()
            if conversation:
                print(f"找到关联对话 ID: {conversation.id}")
                
                # 获取对话中的所有消息ID
                from app.models.enhanced_models import ConversationMessage
                messages = db.query(ConversationMessage).filter(
                    ConversationMessage.conversation_id == conversation.id
                ).all()
                message_ids = [msg.id for msg in messages]
                print(f"找到 {len(messages)} 条对话消息，ID: {message_ids}")
                
                # 先删除AI交互记录
                if message_ids:
                    from app.models.conversation_models import AIInteraction
                    ai_interaction_count = db.query(AIInteraction).filter(
                        AIInteraction.message_id.in_(message_ids)
                    ).count()
                    print(f"找到 {ai_interaction_count} 个AI交互记录")
                    
                    deleted_ai_interactions = db.query(AIInteraction).filter(
                        AIInteraction.message_id.in_(message_ids)
                    ).delete(synchronize_session=False)
                    print(f"删除了 {deleted_ai_interactions} 个AI交互记录")
                
                # 再删除对话消息
                deleted_messages = db.query(ConversationMessage).filter(
                    ConversationMessage.conversation_id == conversation.id
                ).delete()
                print(f"删除了 {deleted_messages} 条对话消息")
                
                # 最后删除对话
                db.delete(conversation)
                print("关联对话删除成功")
            else:
                print("未找到关联对话")
        except Exception as conv_error:
            print(f"删除关联对话时出错: {conv_error}")
            raise conv_error
        
        # 删除相关的提醒
        try:
            reminder_count = db.query(Reminder).filter(Reminder.item_id == item_id).count()
            print(f"找到 {reminder_count} 个相关提醒")
            deleted_reminders = db.query(Reminder).filter(Reminder.item_id == item_id).delete()
            print(f"删除了 {deleted_reminders} 个提醒")
        except Exception as reminder_error:
            print(f"删除提醒时出错: {reminder_error}")
            raise reminder_error
        
        # 删除任务步骤
        try:
            from app.models.enhanced_models import TaskStep
            step_count = db.query(TaskStep).filter(TaskStep.item_id == item_id).count()
            print(f"找到 {step_count} 个任务步骤")
            deleted_steps = db.query(TaskStep).filter(TaskStep.item_id == item_id).delete()
            print(f"删除了 {deleted_steps} 个任务步骤")
        except Exception as step_error:
            print(f"删除任务步骤时出错: {step_error}")
            raise step_error
        
        # 删除任务
        try:
            print("开始删除任务本身")
            db.delete(item)
            print("任务删除成功，准备提交事务")
            db.commit()
            print("事务提交成功")
        except Exception as item_error:
            print(f"删除任务时出错: {item_error}")
            raise item_error
        
        return {"success": True, "message": "任务及关联对话删除成功"}
        
    except Exception as e:
        print(f"删除任务失败，详细错误: {str(e)}")
        print(f"错误类型: {type(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除任务失败: {str(e)}"
        )