from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models import schemas
from app.services.ai_service import AIService

router = APIRouter()

@router.post("/analyze", response_model=schemas.AIAnalysisResponse)
async def analyze_text(
    request: schemas.AIAnalysisRequest,
    db: Session = Depends(get_db)
):
    """
    分析用户输入的文本，提取意图、分类和时间信息
    
    例如："明天下午3点打羽毛球" 
    → 分类：运动，时间：明天15:00，创建提醒
    """
    try:
        ai_service = AIService()
        result = await ai_service.analyze_user_input(
            text=request.text,
            timezone=request.user_timezone
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))