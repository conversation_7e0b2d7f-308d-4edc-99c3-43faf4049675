from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.models.enhanced_models import Item, TaskStep, TaskStatus
from app.models.enhanced_schemas import TaskStepCreate, TaskStep as TaskStepSchema
from pydantic import BaseModel

router = APIRouter()

class TaskStepUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    estimated_duration: Optional[int] = None
    step_type: Optional[str] = None
    dependencies: Optional[List[int]] = None
    step_metadata: Optional[dict] = None

class TaskStepModifyRequest(BaseModel):
    action: str  # "add", "update", "delete", "reorder"
    step_data: Optional[TaskStepCreate] = None
    step_update: Optional[TaskStepUpdate] = None
    step_id: Optional[int] = None
    new_order: Optional[List[int]] = None  # 用于重新排序

@router.get("/items/{item_id}/steps", response_model=List[TaskStepSchema])
def get_task_steps(item_id: int, db: Session = Depends(get_db)):
    """获取任务的所有步骤（包括层级结构）"""
    try:
        # 获取主步骤（level=1）
        main_steps = db.query(TaskStep).filter(
            TaskStep.item_id == item_id,
            TaskStep.level == 1
        ).order_by(TaskStep.order_index).all()
        
        # 为每个主步骤加载子步骤
        result = []
        for step in main_steps:
            step_dict = {
                "id": step.id,
                "title": step.title,
                "description": step.description,
                "parent_step_id": step.parent_step_id,
                "order_index": step.order_index,
                "level": step.level,
                "status": step.status,
                "estimated_duration": step.estimated_duration,
                "step_type": step.step_type,
                "dependencies": step.dependencies or [],
                "step_metadata": step.step_metadata or {},
                "sub_steps": []
            }
            
            # 加载子步骤
            sub_steps = db.query(TaskStep).filter(
                TaskStep.parent_step_id == step.id
            ).order_by(TaskStep.order_index).all()
            
            for sub_step in sub_steps:
                step_dict["sub_steps"].append({
                    "id": sub_step.id,
                    "title": sub_step.title,
                    "description": sub_step.description,
                    "parent_step_id": sub_step.parent_step_id,
                    "order_index": sub_step.order_index,
                    "level": sub_step.level,
                    "status": sub_step.status,
                    "estimated_duration": sub_step.estimated_duration,
                    "step_type": sub_step.step_type,
                    "dependencies": sub_step.dependencies or [],
                    "step_metadata": sub_step.step_metadata or {},
                    "sub_steps": []
                })
            
            result.append(step_dict)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务步骤失败: {str(e)}")

@router.post("/items/{item_id}/steps")
def create_task_step(
    item_id: int, 
    step_data: TaskStepCreate, 
    db: Session = Depends(get_db)
):
    """创建新的任务步骤"""
    try:
        # 验证任务是否存在
        item = db.query(Item).filter(Item.id == item_id).first()
        if not item:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 计算order_index
        max_order = db.query(TaskStep).filter(
            TaskStep.item_id == item_id,
            TaskStep.level == step_data.level,
            TaskStep.parent_step_id == step_data.parent_step_id
        ).count()
        
        # 创建新步骤
        new_step = TaskStep(
            item_id=item_id,
            title=step_data.title,
            description=step_data.description,
            parent_step_id=step_data.parent_step_id,
            level=step_data.level,
            order_index=max_order + 1,
            estimated_duration=step_data.estimated_duration,
            step_type=step_data.step_type,
            dependencies=step_data.dependencies,
            step_metadata=step_data.step_metadata or {}
        )
        
        db.add(new_step)
        db.commit()
        db.refresh(new_step)
        
        return {
            "id": new_step.id,
            "message": "步骤创建成功",
            "step": {
                "id": new_step.id,
                "title": new_step.title,
                "description": new_step.description,
                "order_index": new_step.order_index,
                "level": new_step.level,
                "status": new_step.status.value
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建步骤失败: {str(e)}")

@router.put("/steps/{step_id}")
def update_task_step(
    step_id: int, 
    step_update: TaskStepUpdate, 
    db: Session = Depends(get_db)
):
    """更新任务步骤"""
    try:
        step = db.query(TaskStep).filter(TaskStep.id == step_id).first()
        if not step:
            raise HTTPException(status_code=404, detail="步骤不存在")
        
        # 更新字段
        update_data = step_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(step, field, value)
        
        db.commit()
        db.refresh(step)
        
        return {
            "message": "步骤更新成功",
            "step": {
                "id": step.id,
                "title": step.title,
                "description": step.description,
                "status": step.status.value
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新步骤失败: {str(e)}")

@router.delete("/steps/{step_id}")
def delete_task_step(step_id: int, db: Session = Depends(get_db)):
    """删除任务步骤"""
    try:
        step = db.query(TaskStep).filter(TaskStep.id == step_id).first()
        if not step:
            raise HTTPException(status_code=404, detail="步骤不存在")
        
        # 检查是否有子步骤
        sub_steps_count = db.query(TaskStep).filter(TaskStep.parent_step_id == step_id).count()
        if sub_steps_count > 0:
            raise HTTPException(status_code=400, detail="请先删除所有子步骤")
        
        db.delete(step)
        db.commit()
        
        return {"message": "步骤删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除步骤失败: {str(e)}")

@router.post("/items/{item_id}/steps/modify")
def modify_task_steps_via_conversation(
    item_id: int,
    request: TaskStepModifyRequest,
    db: Session = Depends(get_db)
):
    """通过对话修改任务步骤（支持自然语言指令）"""
    try:
        if request.action == "add" and request.step_data:
            return create_task_step(item_id, request.step_data, db)
        elif request.action == "update" and request.step_id and request.step_update:
            return update_task_step(request.step_id, request.step_update, db)
        elif request.action == "delete" and request.step_id:
            return delete_task_step(request.step_id, db)
        elif request.action == "reorder" and request.new_order:
            # 重新排序步骤
            for index, step_id in enumerate(request.new_order):
                step = db.query(TaskStep).filter(TaskStep.id == step_id).first()
                if step:
                    step.order_index = index + 1
            db.commit()
            return {"message": "步骤重新排序成功"}
        else:
            raise HTTPException(status_code=400, detail="无效的修改请求")
            
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"修改步骤失败: {str(e)}")
