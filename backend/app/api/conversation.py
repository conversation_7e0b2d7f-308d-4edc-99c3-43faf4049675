from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.services.conversation_service import ConversationService
from app.models.enhanced_schemas import ConversationInput, ConversationResponse
from pydantic import BaseModel

router = APIRouter()

class ChatRequest(BaseModel):
    text: str
    conversation_id: Optional[int] = None
    user_id: int = 1

class ChatResponse(BaseModel):
    conversation_id: int
    message_id: Optional[int] = None
    response: str
    # 移除analysis字段，不向前端暴露技术信息
    task_created: Optional[bool] = None  # 是否创建了任务
    task_info: Optional[dict] = None     # 任务信息（仅在创建成功时）
    context: Optional[dict] = None

@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, db: Session = Depends(get_db)):
    """
    处理对话输入，支持多轮对话和上下文管理
    """
    try:
        service = ConversationService(db)
        result = await service.process_conversation(
            user_id=request.user_id,
            text=request.text,
            conversation_id=request.conversation_id
        )
        
        return ChatResponse(
            conversation_id=result['conversation_id'],
            message_id=result.get('message_id'),
            response=result['response'],
            task_created=result.get('task_created', False),
            task_info=result.get('task_info'),
            context=result.get('context')
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")

@router.post("/conversations/new")
def create_new_conversation(user_id: int = 1, db: Session = Depends(get_db)):
    """创建新对话"""
    try:
        from app.models.enhanced_models import Conversation, ConversationStatus
        
        conversation = Conversation(
            user_id=user_id,
            title="新对话",
            status=ConversationStatus.ACTIVE
        )
        db.add(conversation)
        db.commit()
        db.refresh(conversation)
        
        return {
            "conversation_id": conversation.id,
            "title": conversation.title,
            "status": conversation.status.value,
            "created_at": conversation.created_at.isoformat(),
            "updated_at": conversation.updated_at.isoformat()
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建新对话失败: {str(e)}")

@router.get("/conversations")
def get_conversations(user_id: int = 1, db: Session = Depends(get_db)):
    """获取用户对话列表"""
    try:
        service = ConversationService(db)
        conversations = service.get_conversations(user_id)
        return {"conversations": conversations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")

@router.get("/conversations/{conversation_id}")
def get_conversation_detail(conversation_id: int, user_id: int = 1, db: Session = Depends(get_db)):
    """获取对话详细信息，包含调试信息"""
    try:
        service = ConversationService(db)
        detail = service.get_conversation_detail(conversation_id, user_id)
        if not detail:
            raise HTTPException(status_code=404, detail="对话不存在")
        return detail
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话详情失败: {str(e)}")

@router.get("/conversations/{conversation_id}/debug")
def get_conversation_debug(conversation_id: int, user_id: int = 1, db: Session = Depends(get_db)):
    """获取对话的调试信息，包含AI交互详情"""
    try:
        service = ConversationService(db)
        detail = service.get_conversation_detail(conversation_id, user_id)
        if not detail:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        # 只返回调试相关信息
        return {
            "conversation_id": conversation_id,
            "debug_interactions": detail.get('debug_interactions', []),
            "message_count": len(detail.get('messages', [])),
            "total_processing_time": sum(
                inter.get('processing_time_ms', 0) 
                for inter in detail.get('debug_interactions', [])
            )
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调试信息失败: {str(e)}")

@router.post("/conversations/{conversation_id}/continue", response_model=ConversationResponse)
async def continue_conversation(
    conversation_id: int,
    message: dict,  # {"content": "用户输入", "is_voice_input": false}
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """继续现有对话"""
    try:
        conversation_input = ConversationInput(
            conversation_id=conversation_id,
            message=message.get("content", ""),
            is_voice_input=message.get("is_voice_input", False)
        )
        
        conversation_service = ConversationService(db)
        result = await conversation_service.process_conversation(
            user_id=current_user_id,
            text=conversation_input.message,
            conversation_id=conversation_input.conversation_id
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"继续对话失败: {str(e)}")

@router.delete("/conversations/{conversation_id}")
def delete_conversation(
    conversation_id: int,
    db: Session = Depends(get_db),
    current_user_id: int = 1
):
    """删除对话"""
    try:
        from app.models.enhanced_models import Conversation, ConversationMessage
        
        # 删除对话消息
        db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).delete()
        
        # 删除对话
        deleted_count = db.query(Conversation).filter(
            Conversation.id == conversation_id,
            Conversation.user_id == current_user_id
        ).delete()
        
        if deleted_count == 0:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        db.commit()
        return {"message": "对话删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")