import httpx
import json
from typing import Dict, Any, Optional
from app.core.config import settings

class QianWenService:
    """通义千问API服务"""
    
    def __init__(self):
        self.api_key = settings.QIANWEN_API_KEY
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.model = "qwen-turbo"
    
    async def chat_completion(self, messages: list, max_tokens: int = 1500) -> Dict[str, Any]:
        """调用通义千问API"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "input": {
                "messages": messages
            },
            "parameters": {
                "max_tokens": max_tokens,
                "temperature": 0.7,
                "top_p": 0.8
            }
        }
        
        async with httpx.AsyncClient(
            trust_env=False,  # 不信任环境变量中的代理设置
            timeout=30.0
        ) as client:
            response = await client.post(
                self.base_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")

class VolcEngineService:
    """火山引擎API服务"""
    
    def __init__(self):
        self.api_key = settings.VOLCENGINE_API_KEY
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.model = "ep-20231002125419-xxx"  # 需要替换为实际的模型ID
    
    async def chat_completion(self, messages: list, max_tokens: int = 1500) -> Dict[str, Any]:
        """调用火山引擎API"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": 0.7,
            "top_p": 0.8
        }
        
        async with httpx.AsyncClient(
            trust_env=False,  # 不信任环境变量中的代理设置
            timeout=30.0
        ) as client:
            response = await client.post(
                self.base_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")

class AIModelClient:
    """AI模型客户端，支持多个AI服务提供商"""
    
    def __init__(self, provider: str = "qianwen"):
        self.provider = provider
        
        if provider == "qianwen":
            self.client = QianWenService()
        elif provider == "volcengine":
            self.client = VolcEngineService()
        else:
            raise ValueError(f"不支持的AI服务提供商: {provider}")
    
    async def analyze_text(self, text: str, current_time: str, timezone: str) -> Dict[str, Any]:
        """使用AI模型分析文本"""
        
        system_prompt = """你是一个智能助手，专门负责分析用户输入并提取关键信息。

请分析用户输入的文本，识别以下信息：
1. 活动类别（运动、健康、工作、学习、生活、娱乐、购物、会议）
2. 任务标题
3. 详细描述
4. 时间信息（如果有的话）
5. 是否需要提醒
6. 如果是项目类任务，建议的执行步骤

请严格按照以下JSON格式返回结果：
{
    "category": "分类名称",
    "title": "任务标题",
    "description": "详细描述",
    "has_time": true/false,
    "time_expression": "原始时间表达式",
    "absolute_time": "2024-01-01 15:00:00",
    "content": {
        "steps": ["步骤1", "步骤2"],
        "details": "其他信息"
    },
    "confidence": 0.9
}

注意：
- 时间解析需要考虑当前时间和时区
- 相对时间（明天、后天、下周等）需要转换为具体时间
- 置信度范围0-1，反映分析准确性"""

        user_prompt = f"""
当前时间：{current_time}
时区：{timezone}
用户输入：{text}

请分析上述用户输入。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = await self.client.chat_completion(messages)
            
            # 解析响应（不同服务商的响应格式可能不同）
            if self.provider == "qianwen":
                content = response["output"]["text"]
            elif self.provider == "volcengine":
                content = response["choices"][0]["message"]["content"]
            
            # 尝试解析JSON
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                # 如果解析失败，返回基本分析
                return self._fallback_analysis(text)
                
        except Exception as e:
            print(f"AI分析失败: {e}")
            return self._fallback_analysis(text)
    
    def _fallback_analysis(self, text: str) -> Dict[str, Any]:
        """AI调用失败时的备用分析"""
        return {
            "category": "生活",
            "title": text[:30],
            "description": f"用户输入：{text}",
            "has_time": False,
            "time_expression": None,
            "absolute_time": None,
            "content": {"details": "AI分析不可用，使用基础解析"},
            "confidence": 0.3
        }