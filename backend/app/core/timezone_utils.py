"""
时区工具函数
提供时区感知的时间处理功能
"""
from datetime import datetime
import pytz

# 默认时区设置为东八区
DEFAULT_TIMEZONE = "Asia/Shanghai"

def get_local_now(timezone_str: str = DEFAULT_TIMEZONE) -> datetime:
    """
    获取指定时区的当前时间
    
    Args:
        timezone_str: 时区字符串，默认为 Asia/Shanghai
        
    Returns:
        带时区信息的当前时间
    """
    tz = pytz.timezone(timezone_str)
    return datetime.now(tz)

def get_local_now_naive(timezone_str: str = DEFAULT_TIMEZONE) -> datetime:
    """
    获取指定时区的当前时间（naive datetime，用于数据库存储）
    
    Args:
        timezone_str: 时区字符串，默认为 Asia/Shanghai
        
    Returns:
        本地时间（不带时区信息）
    """
    tz = pytz.timezone(timezone_str)
    return datetime.now(tz).replace(tzinfo=None)

def utc_to_local(utc_dt: datetime, timezone_str: str = DEFAULT_TIMEZONE) -> datetime:
    """
    将UTC时间转换为本地时间
    
    Args:
        utc_dt: UTC时间
        timezone_str: 目标时区
        
    Returns:
        本地时间
    """
    if utc_dt.tzinfo is None:
        utc_dt = pytz.utc.localize(utc_dt)
    
    local_tz = pytz.timezone(timezone_str)
    return utc_dt.astimezone(local_tz)

def local_to_utc(local_dt: datetime, timezone_str: str = DEFAULT_TIMEZONE) -> datetime:
    """
    将本地时间转换为UTC时间
    
    Args:
        local_dt: 本地时间
        timezone_str: 源时区
        
    Returns:
        UTC时间
    """
    if local_dt.tzinfo is None:
        local_tz = pytz.timezone(timezone_str)
        local_dt = local_tz.localize(local_dt)
    
    return local_dt.astimezone(pytz.utc)