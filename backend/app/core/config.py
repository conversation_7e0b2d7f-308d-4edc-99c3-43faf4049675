from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql://betterplan:plan123456@localhost/aiagent"
    REDIS_URL: str = "redis://localhost:6379"
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI模型配置
    QIANWEN_API_KEY: str = "sk-8137864e6cae4b07b1e3e214f4c70bb9"
    VOLCENGINE_API_KEY: str = ""
    
    class Config:
        env_file = ".env"

settings = Settings()