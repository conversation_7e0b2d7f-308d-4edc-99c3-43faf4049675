import json
import re
from typing import Dict, Any, Optional
from .ai_service import AIService

class TitleGenerationService:
    """对话标题生成服务"""
    
    def __init__(self):
        self.ai_service = AIService()
    
    def generate_conversation_title(self, conversation_content: str, ai_analysis: Optional[Dict[str, Any]] = None) -> str:
        """
        根据对话内容生成有意义的标题
        
        Args:
            conversation_content: 对话内容（用户输入）
            ai_analysis: AI分析结果（可选）
            
        Returns:
            生成的标题字符串
        """
        try:
            # 首先尝试从AI分析结果中提取标题
            if ai_analysis and ai_analysis.get('title'):
                title = ai_analysis['title']
                # 确保标题长度合适
                if len(title) <= 20:
                    return title
                else:
                    # 如果标题太长，截取前20个字符
                    return title[:17] + "..."
            
            # 如果没有AI分析结果或标题，使用规则生成
            return self._generate_title_by_rules(conversation_content)
            
        except Exception as e:
            print(f"标题生成失败: {e}")
            # 降级到简单规则
            return self._generate_simple_title(conversation_content)
    
    def _generate_title_by_rules(self, content: str) -> str:
        """使用规则生成标题"""
        
        # 清理内容
        content = content.strip()
        
        # 常见的任务类型关键词映射
        task_patterns = {
            r'(提醒|记得|别忘了).*?(开会|会议)': '会议提醒',
            r'(提醒|记得|别忘了).*?(吃药|服药)': '服药提醒',
            r'(提醒|记得|别忘了).*?(买|购买)': '购物提醒',
            r'(提醒|记得|别忘了).*?(运动|锻炼|健身)': '运动提醒',
            r'(组织|策划|安排).*?(比赛|活动|聚会)': '活动策划',
            r'(学习|学会|掌握).*?(技能|知识|课程)': '学习计划',
            r'(制定|制作|准备).*?(计划|方案|策略)': '计划制定',
            r'(旅行|旅游|出行).*?(计划|安排)': '旅行计划',
            r'(工作|项目|任务).*?(安排|计划|管理)': '工作安排',
            r'(健康|体检|检查).*?(计划|安排)': '健康管理',
        }
        
        # 尝试匹配任务类型
        for pattern, title in task_patterns.items():
            if re.search(pattern, content):
                return title
        
        # 时间相关的模式
        time_patterns = {
            r'明天.*?(上午|下午|晚上)': '明日安排',
            r'下周.*?(一|二|三|四|五|六|日|天)': '下周计划',
            r'(今天|今晚).*?': '今日事项',
            r'(周末|星期六|星期日).*?': '周末安排',
        }
        
        for pattern, title in time_patterns.items():
            if re.search(pattern, content):
                return title
        
        # 动作关键词提取
        action_keywords = {
            '买': '购物',
            '学': '学习',
            '做': '任务',
            '去': '出行',
            '看': '观看',
            '读': '阅读',
            '写': '写作',
            '练': '练习',
            '跑': '运动',
            '吃': '用餐',
            '睡': '休息',
            '工作': '工作',
            '开会': '会议',
            '约会': '约会',
            '聚餐': '聚餐',
        }
        
        for keyword, category in action_keywords.items():
            if keyword in content:
                return category
        
        # 如果都没匹配到，使用内容的前几个字
        return self._generate_simple_title(content)
    
    def _generate_simple_title(self, content: str) -> str:
        """生成简单标题"""
        
        # 移除常见的开头词
        content = re.sub(r'^(我想|我要|帮我|请|能否|可以)', '', content)
        content = content.strip()
        
        # 如果内容很短，直接使用
        if len(content) <= 8:
            return content if content else "新对话"
        
        # 尝试提取主要内容
        # 查找第一个动词或名词
        important_words = []
        
        # 常见的重要词汇
        important_patterns = [
            r'(买|购买|采购)([^，。！？]*)',
            r'(学习|学会|掌握)([^，。！？]*)',
            r'(制定|制作|准备)([^，。！？]*)',
            r'(组织|策划|安排)([^，。！？]*)',
            r'(提醒|记得|别忘了)([^，。！？]*)',
            r'(开会|会议|聚会)([^，。！？]*)',
            r'(运动|锻炼|健身)([^，。！？]*)',
            r'(旅行|旅游|出行)([^，。！？]*)',
        ]
        
        for pattern in important_patterns:
            match = re.search(pattern, content)
            if match:
                extracted = match.group(0)
                if len(extracted) <= 10:
                    return extracted
                else:
                    return extracted[:8] + "..."
        
        # 如果没有匹配到特殊模式，取前8个字符
        if len(content) > 8:
            return content[:8] + "..."
        else:
            return content if content else "新对话"
    
    async def generate_title_with_ai(self, conversation_content: str) -> str:
        """使用AI生成更智能的标题（可选的高级功能）"""
        try:
            prompt = f"""
            请为以下对话内容生成一个简洁的标题（不超过10个字）：
            
            用户输入：{conversation_content}
            
            要求：
            1. 标题要简洁明了，不超过10个字
            2. 能够概括对话的主要内容或意图
            3. 使用中文
            4. 不要包含"对话"、"聊天"等词汇
            5. 直接返回标题，不要其他解释
            
            标题：
            """
            
            # 这里可以调用AI服务生成标题
            # 暂时使用规则生成，后续可以接入AI
            return self._generate_title_by_rules(conversation_content)
            
        except Exception as e:
            print(f"AI标题生成失败: {e}")
            return self._generate_title_by_rules(conversation_content)
    
    def update_conversation_title_if_needed(self, conversation, user_input: str, ai_analysis: Optional[Dict[str, Any]] = None) -> bool:
        """
        如果需要，更新对话标题
        
        Args:
            conversation: 对话对象
            user_input: 用户输入
            ai_analysis: AI分析结果
            
        Returns:
            是否更新了标题
        """
        # 只有当标题是"新对话"或为空时才更新
        if conversation.title in ["新对话", "", None]:
            new_title = self.generate_conversation_title(user_input, ai_analysis)
            if new_title and new_title != "新对话":
                conversation.title = new_title
                return True
        
        return False
