import json
from datetime import datetime, timezone, timedelta
from ..core.timezone_utils import get_local_now_naive
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from ..models.enhanced_models import (
    User, Conversation, ConversationMessage, Category, Item, TaskStep, Reminder
)
from ..models.conversation_models import (
    ConversationContext, AIInteraction, TaskTemplate, ReminderSchedule
)
from ..models.enhanced_schemas import ConversationStatus, MessageRole, TaskStatus
from .ai_service import AIService
from .conversation_ai_service import ConversationAIService
from .ios_reminder_service import IOSReminderService
from .title_generation_service import TitleGenerationService
import json
from datetime import datetime
import time

class ConversationService:
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService()
        self.conversation_ai_service = ConversationAIService()
        self.ios_reminder_service = IOSReminderService(db)
        self.title_generation_service = TitleGenerationService()

    def _make_serializable(self, obj):
        """将对象转换为可JSON序列化的格式"""
        if isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'value'):  # 处理枚举类型
            return obj.value
        elif hasattr(obj, '__dict__') and not isinstance(obj, (str, int, float, bool)):
            # 跳过复杂对象
            return str(obj)
        else:
            return obj

    async def process_message(self, user_id: int, text: str, conversation_id: Optional[int] = None, 
                       is_voice: bool = False) -> Dict[str, Any]:
        """处理用户消息"""
        print(f"🚀 [DEBUG] 开始处理用户消息 - 用户ID: {user_id}, 对话ID: {conversation_id}, 消息: {text}")
        
        start_time = time.time()
        
        try:
            # 获取或创建对话
            conversation = self._get_or_create_conversation(user_id, conversation_id)
            print(f"🔍 [DEBUG] 获取/创建对话完成 - 对话ID: {conversation.id}")
            
            # 保存用户消息
            user_message = self._save_user_message(conversation.id, text, is_voice)
            print(f"🔍 [DEBUG] 保存用户消息完成 - 消息ID: {user_message.id}")
            
            # 检查是否有活跃的上下文
            context = self._get_active_context(conversation.id)
            print(f"🔍 [DEBUG] 检查活跃上下文 - 上下文: {context.id if context else 'None'}")
            
            # 根据是否有上下文选择处理方式
            if context:
                print(f"🔍 [DEBUG] 使用上下文处理对话")
                ai_response = await self._handle_context_conversation(conversation, context, text, user_message)
            else:
                print(f"🔍 [DEBUG] 处理新对话")
                ai_response = await self._handle_new_conversation(conversation, text, user_message)
            
            print(f"🔍 [DEBUG] AI响应获取完成: {ai_response}")
            
            # 处理AI分析结果
            analysis_result = self._process_ai_analysis(conversation, ai_response, context)
            print(f"🔍 [DEBUG] AI分析处理完成: {analysis_result}")
            
            # 保存AI回复
            ai_response_text = ai_response.get('response', '抱歉，我无法处理您的请求。')
            
            # 如果是复杂项目，使用分析结果中的自定义响应消息
            if analysis_result.get('context_created') and analysis_result.get('custom_response'):
                ai_response_text = analysis_result['custom_response']
            
            # 检查是否有时间冲突信息需要添加到回复中
            if analysis_result.get('task') and analysis_result['task'].get('time_conflict'):
                conflict_info = analysis_result['task']['time_conflict']
                conflict_message = f"\n\n⚠️ {conflict_info['message']}"
                
                # 列出冲突的任务
                if conflict_info.get('conflicts'):
                    conflict_message += "\n已有任务："
                    for conflict in conflict_info['conflicts']:
                        conflict_time = datetime.fromisoformat(conflict['remind_time']).strftime('%H:%M')
                        conflict_message += f"\n• {conflict['title']} ({conflict_time})"
                
                ai_response_text += conflict_message
            
            ai_message = self._save_ai_message(
                conversation.id, 
                ai_response_text,
                ai_response.get('analysis', {})
            )
            print(f"🔍 [DEBUG] 保存AI消息完成 - 消息ID: {ai_message.id}")
            
            # 记录AI交互
            processing_time = int((time.time() - start_time) * 1000)
            self._record_ai_interaction(
                conversation.id, user_message.id, text, 
                ai_response, processing_time
            )
            print(f"🔍 [DEBUG] 记录AI交互完成 - 处理时间: {processing_time}ms")
            
            # 提交事务
            self.db.commit()
            print(f"🔍 [DEBUG] 数据库事务提交完成")
            
            # 构建响应
            response = {
                'conversation_id': conversation.id,
                'message': ai_response_text,  # 使用包含冲突信息的回复文本
                'analysis': ai_response.get('analysis', {}),
                'result': analysis_result,
                'processing_time': processing_time
            }
            
            print(f"✅ [DEBUG] 消息处理完成，最终响应: {response}")
            return response
            
        except Exception as e:
            print(f"❌ [DEBUG] 处理消息时发生错误: {str(e)}")
            print(f"❌ [DEBUG] 错误类型: {type(e)}")
            import traceback
            print(f"❌ [DEBUG] 错误堆栈: {traceback.format_exc()}")
            
            self.db.rollback()
            
            # 尝试记录错误信息（不引用消息ID，避免外键约束问题）
            try:
                if 'conversation' in locals() and conversation.id:
                    # 确保对话已经存在于数据库中
                    existing_conversation = self.db.query(Conversation).filter(
                        Conversation.id == conversation.id
                    ).first()
                    if existing_conversation:
                        self._record_ai_interaction(
                            conversation.id, None, text,  # message_id设为None
                            {}, int((time.time() - start_time) * 1000), str(e)
                        )
                        self.db.commit()
                        print(f"🔍 [DEBUG] 错误信息记录完成")
            except Exception as record_error:
                print(f"❌ [DEBUG] 记录错误信息失败: {record_error}")
                self.db.rollback()
            raise e

    async def process_conversation(self, user_id: int, text: str, conversation_id: Optional[int] = None) -> Dict[str, Any]:
        """
        处理对话输入，支持多轮对话和上下文管理
        """
        start_time = time.time()
        
        try:
            # 获取或创建对话
            conversation = self._get_or_create_conversation(user_id, conversation_id)
            
            # 检查是否有活跃的上下文
            context = self._get_active_context(conversation.id)
            
            # 保存用户消息
            user_message = self._save_user_message(conversation.id, text)
            
            # 根据上下文决定处理方式
            if context and context.status == "active":
                # 多轮对话处理
                ai_response = await self._handle_context_conversation(conversation, context, text, user_message)
            else:
                # 新对话处理
                ai_response = await self._handle_new_conversation(conversation, text, user_message)
            
            # 记录处理时间
            processing_time = int((time.time() - start_time) * 1000)
            
            # 记录AI交互详情
            self._record_ai_interaction(
                conversation.id, user_message.id, text, 
                ai_response, processing_time
            )
            
            # AI回复已经在具体的处理方法中保存了，避免重复保存
            # ai_message = self._save_ai_message(conversation.id, ai_response.get('response', ''), ai_response)
            ai_message = None  # 临时设置，避免后续引用错误
            
            # 处理AI分析结果
            result = self._process_ai_analysis(conversation, ai_response, context)

            # 更新对话标题（如果需要）
            title_updated = self.title_generation_service.update_conversation_title_if_needed(
                conversation, text, ai_response.get('analysis', {})
            )
            if title_updated:
                print(f"🔍 [DEBUG] 对话标题已更新为: {conversation.title}")

            self.db.commit()
            
            # 构建统一的返回格式
            response_data = {
                'conversation_id': conversation.id,
                'response': ai_response.get('response', ''),
                'context': self._get_context_info(conversation.id),
                'debug_info': {
                    'processing_time_ms': processing_time,
                    'ai_raw_response': ai_response,
                    'user_input': text
                }
            }

            # 添加新流程的字段（如果存在）
            if 'task_created' in ai_response:
                response_data['task_created'] = ai_response['task_created']
                response_data['task_info'] = ai_response.get('task_info')
            else:
                # 向后兼容旧流程
                response_data['task_created'] = False
                response_data['analysis'] = {
                    'intent': ai_response.get('analysis', {}).get('intent', 'unknown'),
                    'confidence': ai_response.get('analysis', {}).get('confidence', 0.0),
                    'category': ai_response.get('analysis', {}).get('category', ''),
                    'title': ai_response.get('analysis', {}).get('title', ''),
                    'complexity': ai_response.get('analysis', {}).get('complexity', 'SIMPLE'),
                    'processing_result': result  # 处理结果（任务创建、提醒创建等）
                }

            return response_data
            
        except Exception as e:
            self.db.rollback()
            # 记录错误 - 不引用消息ID，避免外键约束问题
            try:
                if 'conversation' in locals() and conversation.id:
                    # 确保对话已经存在于数据库中
                    existing_conversation = self.db.query(Conversation).filter(
                        Conversation.id == conversation.id
                    ).first()
                    if existing_conversation:
                        self._record_ai_interaction(
                            conversation.id, None, text,  # message_id设为None
                            {}, int((time.time() - start_time) * 1000), str(e)
                        )
                        self.db.commit()
            except Exception as record_error:
                print(f"记录错误信息失败: {record_error}")
                self.db.rollback()
            raise e

    def _get_or_create_conversation(self, user_id: int, conversation_id: Optional[int] = None) -> Conversation:
        """获取或创建对话"""
        if conversation_id:
            conversation = self.db.query(Conversation).filter(
                Conversation.id == conversation_id,
                Conversation.user_id == user_id
            ).first()
            if conversation:
                return conversation
        
        # 创建新对话
        conversation = Conversation(
            user_id=user_id,
            title="新对话",
            status=ConversationStatus.ACTIVE
        )
        self.db.add(conversation)
        self.db.flush()
        return conversation

    def _get_active_context(self, conversation_id: int) -> Optional[ConversationContext]:
        """获取活跃的对话上下文"""
        return self.db.query(ConversationContext).filter(
            ConversationContext.conversation_id == conversation_id,
            ConversationContext.status == "active"
        ).first()

    def _save_user_message(self, conversation_id: int, content: str, is_voice: bool = False) -> ConversationMessage:
        """保存用户消息"""
        message = ConversationMessage(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            content=content,
            is_voice_input=is_voice
        )
        self.db.add(message)
        self.db.flush()
        return message

    def _save_ai_message(self, conversation_id: int, content: str, analysis: Dict[str, Any]) -> ConversationMessage:
        """保存AI回复消息"""

        # 清理不可序列化的对象
        serializable_analysis = self._make_serializable(analysis)

        message = ConversationMessage(
            conversation_id=conversation_id,
            role=MessageRole.ASSISTANT,
            content=content,
            ai_analysis=serializable_analysis
        )
        self.db.add(message)
        self.db.flush()
        return message

    async def _handle_new_conversation(self, conversation: Conversation, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理新对话"""
        print(f"🔍 [DEBUG] 开始处理新对话 - 对话ID: {conversation.id}, 用户输入: {text}")
        
        # 获取对话历史（不包括上下文，避免混淆）
        history = self._get_conversation_history(conversation.id, include_context=False)
        print(f"🔍 [DEBUG] 获取对话历史完成，历史消息数量: {len(history)}")
        
        # 使用增强的AI服务进行分析
        print(f"🔍 [DEBUG] 准备调用增强AI服务分析用户输入...")
        try:
            analysis = await self.conversation_ai_service.analyze_conversation(
                user_input=text,
                conversation_history=history,
                timezone="Asia/Shanghai"
            )
            print(f"🔍 [DEBUG] AI分析结果: {analysis.dict()}")

            # 更新对话标题（如果需要）
            title_updated = self.title_generation_service.update_conversation_title_if_needed(
                conversation, text, analysis.dict() if hasattr(analysis, 'dict') else analysis
            )
            if title_updated:
                print(f"🔍 [DEBUG] 对话标题已更新为: {conversation.title}")

            # 根据意图处理不同的情况
            if analysis.intent == "clarify":
                return await self._handle_clarification_request(conversation, analysis, text, user_message)
            elif analysis.intent in ["create_task", "create_project"]:
                return await self._handle_task_creation_new(conversation, analysis, text, user_message)
            else:
                return await self._handle_general_response(conversation, analysis, text, user_message)

        except Exception as e:
            print(f"❌ [DEBUG] AI服务调用失败: {str(e)}")
            # 返回默认响应
            return {
                'response': f'抱歉，我暂时无法理解您的请求。请提供更多详细信息。',
                'analysis': {
                    'intent': 'clarify',
                    'confidence': 0.3,
                    'response_message': '抱歉，我暂时无法理解您的请求。请提供更多详细信息。',
                    'clarification_questions': ['您希望我帮您做什么？', '能否提供更多具体信息？']
                }
            }

    async def _handle_context_conversation(self, conversation: Conversation, context: ConversationContext,
                                   text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理有上下文的多轮对话 - 增强版，支持任务修改和细化"""

        print(f"🔍 [DEBUG] 处理上下文对话 - 上下文类型: {context.context_type}")
        print(f"🔍 [DEBUG] 用户输入: {text}")

        # 获取对话历史
        conversation_history = self._get_conversation_history(conversation.id)

        # 使用增强的AI服务分析上下文对话
        try:
            analysis = await self.conversation_ai_service.analyze_conversation(
                user_input=text,
                conversation_history=conversation_history,
                timezone="Asia/Shanghai"
            )

            print(f"🔍 [DEBUG] 上下文对话AI分析结果: {analysis.dict()}")

            # 根据分析结果处理不同的意图
            if analysis.intent in ["modify_task", "query_task"]:
                return await self._handle_task_modification(context, analysis, text, user_message)
            elif analysis.intent == "create_task":
                # 如果用户想创建新任务，结束当前上下文
                context.status = "completed"
                self.db.flush()
                return await self._handle_new_conversation(conversation, text, user_message)
            else:
                # 继续当前上下文的处理
                return await self._handle_context_continuation(context, analysis, text, user_message)

        except Exception as e:
            print(f"❌ [DEBUG] 上下文对话处理失败: {e}")
            # 降级处理
            return await self._handle_context_fallback(context, text, user_message)

    async def _handle_task_modification(self, context: ConversationContext, analysis, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理任务修改请求"""

        print(f"🔧 [DEBUG] 处理任务修改请求")

        # 获取当前任务信息
        task_info = context.context_data.get('task_info', {})
        current_item_id = None

        # 查找关联的任务
        if context.conversation_id:
            conversation = self.db.query(Conversation).filter(Conversation.id == context.conversation_id).first()
            if conversation and conversation.result_item_id:
                current_item_id = conversation.result_item_id

        if current_item_id:
            # 修改现有任务
            item = self.db.query(Item).filter(Item.id == current_item_id).first()
            if item:
                # 更新任务信息
                if analysis.title and analysis.title != task_info.get('title'):
                    item.title = analysis.title
                if analysis.description:
                    item.description = analysis.description

                # 如果有新的步骤，更新步骤
                if analysis.suggested_steps:
                    # 删除现有步骤
                    self.db.query(TaskStep).filter(TaskStep.item_id == current_item_id).delete()
                    # 创建新步骤
                    self._create_task_steps(current_item_id, analysis.suggested_steps)

                self.db.flush()

                response_message = f"已更新任务「{item.title}」"
                if analysis.suggested_steps:
                    response_message += f"，包含 {len(analysis.suggested_steps)} 个步骤"

                return {
                    'response': response_message,
                    'analysis': analysis.dict(),
                    'task': {
                        'item_id': item.id,
                        'title': item.title,
                        'category': item.category.name if item.category else '未分类',
                        'complexity': item.complexity.value,
                        'updated': True
                    }
                }

        # 如果没有找到现有任务，创建新任务
        return await self._create_task_from_analysis(analysis.dict(), context.conversation_id)

    async def _handle_context_continuation(self, context: ConversationContext, analysis, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理上下文延续"""

        print(f"🔄 [DEBUG] 处理上下文延续")

        # 更新上下文数据
        if hasattr(analysis, 'context_reference') and analysis.context_reference:
            context.context_data['last_reference'] = analysis.context_reference

        # 更新收集的信息
        if 'collected_info' not in context.context_data:
            context.context_data['collected_info'] = {}

        context.context_data['collected_info']['last_input'] = text
        context.updated_at = get_local_now_naive()
        self.db.flush()

        return {
            'response': analysis.response_message,
            'analysis': analysis.dict(),
            'context_updated': True
        }

    async def _handle_context_fallback(self, context: ConversationContext, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """上下文处理失败时的降级方案"""

        print(f"⚠️ [DEBUG] 使用上下文处理降级方案")

        return {
            'response': f"我理解您的输入：{text}，请继续提供更多信息。",
            'analysis': {
                'intent': 'context_continuation',
                'response': f"我理解您的输入：{text}，请继续提供更多信息。",
                'confidence': 0.7,
                'context_update': {'last_input': text}
            }
        }

    def _build_context_prompt(self, context: ConversationContext, current_input: str) -> str:
        """构建上下文相关的prompt"""
        context_info = f"""
        当前对话上下文：
        - 类型：{context.context_type}
        - 当前步骤：{context.current_step}/{context.total_steps}
        - 上下文数据：{json.dumps(context.context_data, ensure_ascii=False)}
        - 需要确认：{context.needs_confirmation}
        
        用户当前输入：{current_input}
        
        请根据上下文继续处理用户的请求。
        """
        return context_info

    def _update_context(self, context: ConversationContext, ai_response: Dict[str, Any]):
        """更新对话上下文"""
        analysis = ai_response.get('analysis', {})
        
        # 更新步骤
        if analysis.get('next_step'):
            context.current_step = analysis.get('next_step', context.current_step + 1)
        
        # 更新上下文数据
        if analysis.get('context_update'):
            if context.context_data is None:
                context.context_data = {}
            context.context_data.update(analysis['context_update'])
        
        # 检查是否完成
        if analysis.get('context_completed') or context.current_step >= context.total_steps:
            context.status = "completed"
        
        context.updated_at = get_local_now_naive()
        self.db.flush()

    def _process_ai_analysis(self, conversation: Conversation, ai_response: Dict[str, Any],
                           context: Optional[ConversationContext]) -> Dict[str, Any]:
        """处理AI分析结果"""
        print(f"🔍 [DEBUG] 开始处理AI分析结果: {ai_response}")

        # 检查是否是新的流程（包含task_created字段）
        if 'task_created' in ai_response:
            print(f"🔍 [DEBUG] 检测到新流程响应，直接返回")
            # 新流程已经处理完成，直接返回结果
            return ai_response

        # 旧流程处理逻辑（向后兼容）
        analysis = ai_response.get('analysis', {})
        print(f"🔍 [DEBUG] 提取的分析结果: {analysis}")

        # 验证并纠正复杂度判断
        analysis = self._validate_and_correct_complexity(analysis)
        # 更新回原始响应中
        ai_response['analysis'] = analysis

        # 注意：在这个位置我们没有直接的用户输入，标题更新已在上层方法中处理

        result = {}

        intent = analysis.get('intent')
        print(f"🔍 [DEBUG] 识别的意图: {intent}")

        # 任务创建现在由新的流程处理，这里不再处理
        if intent in ['task_creation', 'create_task', 'create_project']:
            print(f"🔍 [DEBUG] 任务创建意图已由新流程处理，跳过旧流程")
        
        elif intent == 'reminder':
            print(f"🔍 [DEBUG] 处理提醒创建...")
            reminder_result = self._handle_reminder_creation(conversation.user_id, analysis)
            result['reminder'] = reminder_result
            print(f"🔍 [DEBUG] 创建提醒完成: {reminder_result}")
            
            if reminder_result:
                conversation.status = ConversationStatus.COMPLETED
                conversation.result_item_id = reminder_result.get('item_id')
                
                # 创建iOS提醒
                self._create_ios_reminder(reminder_result)
                print(f"🔍 [DEBUG] 创建iOS提醒完成")
        
        elif intent == 'task_completion':
            print(f"🔍 [DEBUG] 处理任务完成...")
            completion_result = self._handle_task_completion(conversation.user_id, analysis)
            result['completion'] = completion_result
            print(f"🔍 [DEBUG] 任务完成处理结果: {completion_result}")
        
        else:
            print(f"⚠️ [DEBUG] 未识别的意图，不创建任务")
            # 未识别的意图不再自动创建任务，避免重复
        
        print(f"🔍 [DEBUG] AI分析处理完成，最终结果: {result}")
        return result

    def _create_task_context(self, conversation_id: int, analysis: Dict[str, Any]) -> ConversationContext:
        """为复杂任务创建上下文"""
        context = ConversationContext(
            conversation_id=conversation_id,
            context_type="complex_task_creation",
            current_step=1,
            total_steps=analysis.get('estimated_steps', 3),
            context_data={
                'task_info': analysis,
                'collected_info': {}
            },
            needs_confirmation=True,
            status="active"
        )
        self.db.add(context)
        self.db.flush()
        return context

    def _record_ai_interaction(self, conversation_id: int, message_id: Optional[int], user_input: str,
                             ai_response: Dict[str, Any], processing_time: int, error: str = None):
        """记录AI交互详情"""
        try:
            # 清理AI响应中的不可序列化对象
            serializable_response = self._make_serializable(ai_response)

            interaction = AIInteraction(
                conversation_id=conversation_id,
                message_id=message_id,  # 现在可以为None
                user_input=user_input,
                ai_prompt=serializable_response.get('prompt', ''),
                ai_raw_response=json.dumps(serializable_response, ensure_ascii=False),
                ai_processed_response=serializable_response.get('response', ''),
                analysis_result=serializable_response.get('analysis', {}),
                processing_time_ms=processing_time,
                error_message=error
            )
            self.db.add(interaction)
            self.db.flush()
        except Exception as e:
            print(f"❌ [DEBUG] 记录AI交互失败: {e}")
            # 不要因为记录失败而影响主流程

    def _get_conversation_history(self, conversation_id: int, include_context: bool = True) -> List[Dict]:
        """获取对话历史"""
        messages = self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.timestamp).all()
        
        history = []
        for msg in messages:
            history.append({
                "role": msg.role.value,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat()
            })
        
        return history

    def _get_context_info(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """获取上下文信息"""
        context = self._get_active_context(conversation_id)
        if not context:
            return None
        
        return {
            'type': context.context_type,
            'current_step': context.current_step,
            'total_steps': context.total_steps,
            'needs_confirmation': context.needs_confirmation,
            'status': context.status,
            'data': context.context_data
        }

    def _get_conversation_history(self, conversation_id: int, include_context: bool = True) -> List[Dict]:
        """获取对话历史"""
        messages = self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.timestamp).all()

        history = []
        for msg in messages:
            history.append({
                'role': msg.role.value,
                'content': msg.content,
                'timestamp': msg.timestamp.isoformat()
            })

        return history

    async def _handle_clarification_request(self, conversation: Conversation, analysis, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理澄清请求 - 收集更多信息而不是立即创建任务"""

        print(f"❓ [DEBUG] 处理澄清请求 - 需要收集更多信息")

        # 创建信息收集上下文
        context = ConversationContext(
            conversation_id=conversation.id,
            context_type="information_gathering",
            current_step=1,
            total_steps=3,
            context_data={
                'initial_request': text,
                'information_needed': analysis.information_needed,
                'collected_info': {},
                'clarification_questions': analysis.clarification_questions
            },
            needs_confirmation=True,
            status="active"
        )
        self.db.add(context)
        self.db.flush()

        # 保存AI回复
        ai_message = self._save_ai_message(
            conversation.id,
            analysis.response_message,
            {}  # 不保存技术分析信息
        )

        return {
            'response': analysis.response_message,
            'conversation_id': conversation.id,
            'task_created': False,
            'context_created': True,
            'needs_more_info': True
        }

    async def _handle_task_creation_new(self, conversation: Conversation, analysis, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理任务创建 - 自然对话式处理"""

        print(f"✅ [DEBUG] 处理任务创建 - 意图: {analysis.intent}")

        # 检查信息是否充分
        if analysis.confidence < 0.8 or not analysis.title:
            print(f"⚠️ [DEBUG] 信息不充分，转为澄清模式")
            # 信息不充分，转为澄清模式 - 使用自然语言回复
            analysis.intent = "clarify"
            analysis.response_message = f"好的，您想{analysis.description or '做一件事情'}。请告诉我更多详细信息，这样我能更好地帮您规划。"
            analysis.clarification_questions = [
                "具体要做什么呢？",
                "有时间要求吗？",
                "需要准备什么？"
            ]
            return await self._handle_clarification_request(conversation, analysis, text, user_message)

        # 信息充分，创建任务
        serializable_analysis = self._make_serializable(analysis.dict())
        task_result = self._handle_task_creation(conversation.user_id, serializable_analysis)

        # 更新对话状态
        conversation.status = ConversationStatus.COMPLETED
        conversation.result_item_id = task_result.get('item_id')
        self.db.flush()

        # 根据任务类型生成自然的回复消息
        if analysis.complexity.value == 'COMPLEX':
            # 复杂项目：生成项目创建成功的自然回复
            response_message, task_info = self._generate_complex_project_response(task_result, serializable_analysis)
        else:
            # 简单任务：生成简洁的成功回复
            response_message, task_info = self._generate_simple_task_response(task_result, serializable_analysis)

        # 保存AI回复
        ai_message = self._save_ai_message(
            conversation.id,
            response_message,
            {}  # 不保存技术分析信息
        )

        return {
            'response': response_message,
            'task_created': True,
            'task_info': task_info,
            'conversation_id': conversation.id
        }

    def _generate_complex_project_response(self, task_result: Dict[str, Any], analysis: Dict[str, Any]) -> tuple[str, dict]:
        """为复杂项目生成自然的回复消息和任务信息"""

        item_id = task_result.get('item_id')
        title = analysis.get('title', '项目')

        # 获取任务步骤
        steps = []
        if item_id:
            try:
                from ..models.enhanced_models import TaskStep
                task_steps = self.db.query(TaskStep).filter(
                    TaskStep.item_id == item_id
                ).order_by(TaskStep.order_index).all()

                steps = [
                    {
                        'title': step.title,
                        'type': step.step_type,
                        'order': step.order_index,
                        'level': step.level,
                        'description': step.description
                    }
                    for step in task_steps
                ]
            except Exception as e:
                print(f"❌ [DEBUG] 获取任务步骤失败: {e}")

        # 生成包含步骤的详细回复消息
        response = f"✅ 已为您创建项目「{title}」\n\n"

        if steps:
            response += f"📋 项目包含 {len(steps)} 个步骤：\n"
            for step in steps:
                step_type_emoji = {
                    'preparation': '🔧',
                    'execution': '⚡',
                    'review': '✅',
                    'cleanup': '🧹'
                }.get(step['type'], '📝')
                response += f"{step['order']}. {step_type_emoji} {step['title']}\n"

            response += "\n💡 我已经为关键步骤设置了智能提醒，您可以按计划执行。"
        else:
            response += "📋 项目已创建，您可以开始执行了！"

        # 任务信息（用于前端显示详细信息）
        task_info = {
            'id': item_id,
            'title': title,
            'type': 'project',
            'complexity': 'COMPLEX',
            'steps': steps,
            'step_count': len(steps),
            'has_smart_reminders': len(steps) > 0
        }

        return response, task_info

    def _generate_simple_task_response(self, task_result: Dict[str, Any], analysis: Dict[str, Any]) -> tuple[str, dict]:
        """为简单任务生成自然的回复消息和任务信息"""

        item_id = task_result.get('item_id')
        title = analysis.get('title', '任务')

        # 检查是否有时间信息
        time_info = ""
        if analysis.get('has_time') and analysis.get('time_expression'):
            time_info = f"，时间是{analysis.get('time_expression')}"

        # 生成自然的回复消息
        response = f"好的，已经为您设置了「{title}」{time_info}。"

        # 任务信息
        task_info = {
            'id': item_id,
            'title': title,
            'type': 'task',
            'complexity': 'SIMPLE',
            'time_expression': analysis.get('time_expression')
        }

        return response, task_info

    async def _handle_general_response(self, conversation: Conversation, analysis, text: str, user_message: ConversationMessage) -> Dict[str, Any]:
        """处理一般回复"""

        print(f"💬 [DEBUG] 处理一般回复 - 意图: {analysis.intent}")

        # 保存AI回复
        ai_message = self._save_ai_message(
            conversation.id,
            analysis.response_message,
            {}  # 不保存技术分析信息
        )

        return {
            'response': analysis.response_message,
            'conversation_id': conversation.id,
            'task_created': False
        }

    def _create_task_steps(self, item_id: int, steps: List[str]):
        """创建任务步骤，支持智能分类和层级结构"""

        # 分析步骤，识别准备、执行、检查、收尾阶段
        categorized_steps = self._categorize_steps(steps)

        order_index = 1
        for category, step_list in categorized_steps.items():
            for step_title in step_list:
                # 判断步骤类型
                step_type = self._determine_step_type(step_title, category)

                # 创建主步骤
                step = TaskStep(
                    item_id=item_id,
                    title=step_title,
                    description=f"这是{category}阶段的步骤",
                    order_index=order_index,
                    level=1,
                    status=TaskStatus.DRAFT,
                    step_type=step_type,
                    dependencies=[],
                    step_metadata={
                        'category': category,
                        'auto_generated': True
                    }
                )
                self.db.add(step)
                self.db.flush()  # 获取step.id

                # 如果是复杂步骤，可能需要创建子步骤
                if self._is_complex_step(step_title):
                    sub_steps = self._generate_sub_steps(step_title)
                    for i, sub_step_title in enumerate(sub_steps):
                        sub_step = TaskStep(
                            item_id=item_id,
                            parent_step_id=step.id,
                            title=sub_step_title,
                            description=f"{step_title}的子任务",
                            order_index=i + 1,
                            level=2,
                            status=TaskStatus.DRAFT,
                            step_type="execution",
                            dependencies=[],
                            step_metadata={
                                'parent_category': category,
                                'auto_generated': True
                            }
                        )
                        self.db.add(sub_step)

                order_index += 1

        self.db.flush()

    def _categorize_steps(self, steps: List[str]) -> dict:
        """将步骤分类到不同阶段"""
        categories = {
            'preparation': [],  # 准备阶段
            'execution': [],    # 执行阶段
            'review': [],       # 检查阶段
            'cleanup': []       # 收尾阶段
        }

        preparation_keywords = ['准备', '购买', '联系', '预订', '申请', '规划', '设计', '制定']
        review_keywords = ['检查', '确认', '验证', '测试', '审核', '评估']
        cleanup_keywords = ['清理', '整理', '总结', '归档', '收尾', '结束']

        for step in steps:
            if any(keyword in step for keyword in preparation_keywords):
                categories['preparation'].append(step)
            elif any(keyword in step for keyword in review_keywords):
                categories['review'].append(step)
            elif any(keyword in step for keyword in cleanup_keywords):
                categories['cleanup'].append(step)
            else:
                categories['execution'].append(step)

        # 移除空分类
        return {k: v for k, v in categories.items() if v}

    def _determine_step_type(self, step_title: str, category: str) -> str:
        """根据步骤标题和分类确定步骤类型"""
        type_mapping = {
            'preparation': 'preparation',
            'execution': 'execution',
            'review': 'review',
            'cleanup': 'cleanup'
        }
        return type_mapping.get(category, 'execution')

    def _is_complex_step(self, step_title: str) -> bool:
        """判断是否为复杂步骤，需要进一步分解"""
        complex_keywords = ['组织', '策划', '准备装备', '制定规则', '安排人员']
        return any(keyword in step_title for keyword in complex_keywords)

    def _generate_sub_steps(self, step_title: str) -> List[str]:
        """为复杂步骤生成子步骤"""
        if '准备装备' in step_title or '装备' in step_title:
            return ['列出所需装备清单', '检查装备状态', '购买缺失装备', '测试装备功能']
        elif '组织' in step_title or '策划' in step_title:
            return ['确定参与人员', '制定时间安排', '分配具体任务', '建立沟通渠道']
        elif '规则' in step_title:
            return ['研究相关规定', '制定比赛规则', '准备规则说明', '向参与者解释规则']
        else:
            return [f'完成{step_title}的具体操作']

    def _create_smart_reminders(self, item_id: int, user_id: int, analysis: Dict[str, Any]):
        """为复杂项目创建智能提醒"""
        try:
            from datetime import datetime, timedelta
            from dateutil import parser

            # 解析项目的目标时间
            target_time_str = analysis.get('absolute_time')
            if not target_time_str:
                return

            target_time = parser.parse(target_time_str)

            # 获取项目步骤
            from ..models.enhanced_models import TaskStep
            steps = self.db.query(TaskStep).filter(
                TaskStep.item_id == item_id
            ).order_by(TaskStep.order_index).all()

            if not steps:
                return

            # 智能提醒策略：根据步骤类型和项目时间安排提醒
            reminder_schedule = self._calculate_reminder_schedule(target_time, len(steps))

            for i, step in enumerate(steps):
                remind_time = reminder_schedule.get(step.step_type, target_time - timedelta(days=1))

                # 确保提醒时间不早于当前时间
                now = datetime.now(target_time.tzinfo) if target_time.tzinfo else datetime.now()
                if remind_time <= now:
                    remind_time = now + timedelta(minutes=30)  # 30分钟后提醒

                reminder = Reminder(
                    item_id=item_id,
                    user_id=user_id,
                    remind_time=remind_time,
                    title=f"📋 项目步骤提醒：{step.title}",
                    message=f"该执行「{analysis.get('title', '项目')}」的第{step.order_index}步了：{step.title}"
                )
                self.db.add(reminder)

                print(f"🔔 [DEBUG] 创建提醒: {step.title} -> {remind_time}")

        except Exception as e:
            print(f"❌ [DEBUG] 创建智能提醒失败: {e}")

    def _calculate_reminder_schedule(self, target_time: datetime, step_count: int) -> Dict[str, datetime]:
        """计算智能提醒时间表"""
        from datetime import timedelta

        # 基于目标时间计算各类步骤的提醒时间
        schedule = {}

        # 准备类步骤：提前较多时间
        schedule['preparation'] = target_time - timedelta(days=5)

        # 执行类步骤：提前中等时间
        schedule['execution'] = target_time - timedelta(days=2)

        # 检查类步骤：提前较少时间
        schedule['review'] = target_time - timedelta(hours=12)

        # 收尾类步骤：接近目标时间
        schedule['cleanup'] = target_time - timedelta(hours=2)

        # 如果步骤很多，适当调整间隔
        if step_count > 5:
            # 步骤多时，提前更多时间开始
            for key in schedule:
                schedule[key] = schedule[key] - timedelta(days=2)

        return schedule

    def _handle_task_creation(self, user_id: int, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务创建"""
        try:
            # 检查时间冲突（如果有时间信息）
            time_conflict_info = None
            if analysis.get('has_time') and analysis.get('remind_time'):
                time_conflict_info = self._check_time_conflict(user_id, analysis['remind_time'])
            
            # 获取或创建分类
            category_name = analysis.get('category', '生活')
            category = self.db.query(Category).filter(Category.name == category_name).first()
            if not category:
                category = Category(name=category_name, icon="📝", color="#999999")
                self.db.add(category)
                self.db.flush()
            
            # 准备可序列化的分析数据
            serializable_analysis = self._make_serializable(analysis)

            # 创建任务项目
            item = Item(
                title=analysis.get('title', '新任务'),
                description=analysis.get('description', ''),
                category_id=category.id,
                user_id=user_id,
                status=TaskStatus.CONFIRMED,
                priority=analysis.get('priority', 'medium'),
                complexity=analysis.get('complexity', 'SIMPLE'),
                ai_confidence=int(analysis.get('confidence', 0.8) * 100),
                content={'ai_analysis': serializable_analysis}
            )
            self.db.add(item)
            self.db.flush()
            
            # 创建任务步骤（如果是复杂任务）
            print(f"🔍 [DEBUG] 检查任务步骤创建条件:")
            print(f"   复杂度: {analysis.get('complexity')}")
            print(f"   suggested_steps: {analysis.get('suggested_steps', [])}")

            if analysis.get('complexity') == 'COMPLEX' and analysis.get('suggested_steps'):
                print(f"🔍 [DEBUG] 开始创建任务步骤，步骤数量: {len(analysis.get('suggested_steps', []))}")
                self._create_task_steps(item.id, analysis.get('suggested_steps', []))
                print(f"🔍 [DEBUG] 任务步骤创建完成")

                # 为复杂项目创建智能提醒
                if analysis.get('has_time') and analysis.get('absolute_time'):
                    print(f"🔍 [DEBUG] 开始创建智能提醒")
                    self._create_smart_reminders(item.id, user_id, analysis)
                    print(f"🔍 [DEBUG] 智能提醒创建完成")
            
            # 创建提醒（如果有时间信息）
            if analysis.get('has_time') and analysis.get('remind_time'):
                reminder = Reminder(
                    item_id=item.id,
                    user_id=user_id,
                    remind_time=datetime.fromisoformat(analysis['remind_time']),
                    title=f"提醒：{item.title}",
                    message=item.description or ''
                )
                self.db.add(reminder)
            
            self.db.flush()
            
            result = {
                'item_id': item.id,
                'title': item.title,
                'category': category.name,
                'complexity': item.complexity,
                'created': True
            }
            
            # 如果有时间冲突，添加冲突信息
            if time_conflict_info:
                result['time_conflict'] = time_conflict_info
            
            return result
            
        except Exception as e:
            print(f"任务创建失败: {e}")
            return {'created': False, 'error': str(e)}

    def _check_time_conflict(self, user_id: int, remind_time_str: str) -> Optional[Dict[str, Any]]:
        """检查时间冲突"""
        try:
            target_time = datetime.fromisoformat(remind_time_str)
            
            # 查找同一时间段（前后30分钟）的现有提醒
            time_start = target_time - timedelta(minutes=30)
            time_end = target_time + timedelta(minutes=30)
            
            existing_reminders = self.db.query(Reminder).join(Item).filter(
                Item.user_id == user_id,
                Reminder.remind_time >= time_start,
                Reminder.remind_time <= time_end,
                Item.status != TaskStatus.COMPLETED
            ).all()
            
            if existing_reminders:
                conflicts = []
                for reminder in existing_reminders:
                    item = reminder.item
                    conflicts.append({
                        'item_id': item.id,
                        'title': item.title,
                        'remind_time': reminder.remind_time.isoformat(),
                        'category': item.category.name if item.category else '未分类'
                    })
                
                return {
                    'has_conflict': True,
                    'target_time': target_time.isoformat(),
                    'conflicts': conflicts,
                    'message': f"检测到时间冲突：{target_time.strftime('%m月%d日 %H:%M')} 已有 {len(conflicts)} 个任务"
                }
            
            return None
            
        except Exception as e:
            print(f"时间冲突检查失败: {e}")
            return None

    def _handle_reminder_creation(self, user_id: int, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """处理提醒创建"""
        try:
            # 创建提醒项目
            item = Item(
                title=analysis.get('title', '新提醒'),
                description=analysis.get('description', ''),
                category_id=1,  # 默认分类
                user_id=user_id,
                status=TaskStatus.CONFIRMED,
                priority='medium',
                complexity='SIMPLE',
                content={'type': 'reminder', 'ai_analysis': analysis}
            )
            self.db.add(item)
            self.db.flush()
            
            # 创建提醒
            reminder = Reminder(
                item_id=item.id,
                user_id=user_id,
                remind_time=datetime.fromisoformat(analysis['remind_time']),
                title=analysis.get('title', '提醒'),
                message=analysis.get('description', '')
            )
            self.db.add(reminder)
            self.db.flush()
            
            return {
                'item_id': item.id,
                'reminder_id': reminder.id,
                'title': reminder.title,
                'remind_time': reminder.remind_time.isoformat(),
                'created': True
            }
            
        except Exception as e:
            print(f"提醒创建失败: {e}")
            return {'created': False, 'error': str(e)}

    def _handle_task_completion(self, user_id: int, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务完成"""
        # TODO: 实现任务完成逻辑
        return {'completed': False, 'message': '任务完成功能待实现'}

    def _create_ios_reminder(self, reminder_result: Dict[str, Any]):
        """创建iOS本地提醒"""
        try:
            # 解析提醒时间
            remind_time_str = reminder_result.get('remind_time', '')
            if remind_time_str:
                remind_time = datetime.fromisoformat(remind_time_str.replace('Z', '+00:00'))
            else:
                remind_time = datetime.now() + timedelta(hours=1)  # 默认1小时后提醒
            
            # 调用iOS提醒服务
            ios_result = self.ios_reminder_service.create_ios_reminder(
                item_id=reminder_result.get('item_id'),
                user_id=reminder_result.get('user_id', 1),  # 默认用户ID
                remind_time=remind_time,
                title=reminder_result.get('title', ''),
                message=reminder_result.get('message', ''),
                repeat_type='none'
            )
            
            print(f"iOS提醒创建结果: {ios_result}")
            return ios_result
            
        except Exception as e:
            print(f"创建iOS提醒失败: {e}")
            return None

    def get_conversations(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户对话列表"""
        conversations = self.db.query(Conversation).filter(
            Conversation.user_id == user_id
        ).order_by(Conversation.updated_at.desc()).all()
        
        return [
            {
                'id': conv.id,
                'title': conv.title,
                'status': conv.status.value,
                'created_at': conv.created_at.isoformat(),
                'updated_at': conv.updated_at.isoformat(),
                'result_item_id': conv.result_item_id
            }
            for conv in conversations
        ]

    def get_conversation_detail(self, conversation_id: int, user_id: int) -> Dict[str, Any]:
        """获取对话详细信息"""
        conversation = self.db.query(Conversation).filter(
            Conversation.id == conversation_id,
            Conversation.user_id == user_id
        ).first()
        
        if not conversation:
            return None
        
        messages = self.db.query(ConversationMessage).filter(
            ConversationMessage.conversation_id == conversation_id
        ).order_by(ConversationMessage.timestamp).all()
        
        # 获取AI交互记录
        interactions = self.db.query(AIInteraction).filter(
            AIInteraction.conversation_id == conversation_id
        ).order_by(AIInteraction.created_at).all()
        
        return {
            'conversation': {
                'id': conversation.id,
                'title': conversation.title,
                'status': conversation.status.value,
                'created_at': conversation.created_at.isoformat(),
                'updated_at': conversation.updated_at.isoformat()
            },
            'messages': [
                {
                    'id': msg.id,
                    'role': msg.role.value,
                    'content': msg.content,
                    'timestamp': msg.timestamp.isoformat(),
                    'is_voice_input': msg.is_voice_input,
                    'ai_analysis': msg.ai_analysis
                }
                for msg in messages
            ],
            'debug_interactions': [
                {
                    'id': inter.id,
                    'user_input': inter.user_input,
                    'ai_raw_response': inter.ai_raw_response,
                    'processing_time_ms': inter.processing_time_ms,
                    'error_message': inter.error_message,
                    'created_at': inter.created_at.isoformat()
                }
                for inter in interactions
            ]
        }

    def _message_to_dict(self, message: ConversationMessage) -> Dict[str, Any]:
        """将消息转换为字典"""
        return {
            'id': message.id,
            'role': message.role.value,
            'content': message.content,
            'timestamp': message.timestamp.isoformat(),
            'is_voice_input': message.is_voice_input,
            'ai_analysis': message.ai_analysis
        }

    def _validate_and_correct_complexity(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """验证并纠正复杂度判断"""
        original_complexity = analysis.get('complexity', 'SIMPLE')
        
        # 多种可能的步骤字段路径
        steps = analysis.get('content', {}).get('steps', [])
        suggested_steps = analysis.get('suggested_steps', [])
        details_steps = analysis.get('details', {}).get('steps', []) if isinstance(analysis.get('details'), dict) else []
        
        title = analysis.get('title', '')
        description = analysis.get('description', '')
        
        # 获取步骤数量（检查所有可能的步骤字段）
        all_steps = steps or suggested_steps or details_steps
        step_count = len(all_steps) if isinstance(all_steps, list) else 0
        
        # 调试输出
        print(f"🔍 [DEBUG] 复杂度验证 - 标题: {title}")
        print(f"🔍 [DEBUG] 原始复杂度: {original_complexity}")
        print(f"🔍 [DEBUG] content.steps: {len(steps) if steps else 0}")
        print(f"🔍 [DEBUG] suggested_steps: {len(suggested_steps) if suggested_steps else 0}")
        print(f"🔍 [DEBUG] details.steps: {len(details_steps) if details_steps else 0}")
        print(f"🔍 [DEBUG] 最终步骤数: {step_count}")
        
        # 项目关键词检查
        project_keywords = [
            '比赛', '活动', '项目', '计划', '方案', '策划',
            '组织', '开发', '建设', '设计', '系统', '流程',
            '培训', '管理', '搭建', '制作', '筹备'
        ]
        
        has_project_keywords = any(keyword in title + description for keyword in project_keywords)
        
        # 判断是否需要纠正
        should_be_complex = (
            step_count >= 3 or  # 3个或以上步骤
            has_project_keywords  # 包含项目关键词
        )
        
        corrected_complexity = 'COMPLEX' if should_be_complex else 'SIMPLE'
        
        # 如果需要纠正，记录日志并修改
        if original_complexity.upper() != corrected_complexity.upper():
            print(f"🔧 [DEBUG] 复杂度自动纠正:")
            print(f"    原始判断: {original_complexity}")
            print(f"    步骤数量: {step_count}")
            print(f"    项目关键词: {has_project_keywords}")
            print(f"    修正为: {corrected_complexity}")
            
            analysis['complexity'] = corrected_complexity.upper()
            analysis['auto_corrected'] = True
            analysis['correction_reason'] = f"步骤数量: {step_count}, 项目关键词: {has_project_keywords}"
        else:
            print(f"✅ [DEBUG] 复杂度判断正确: {corrected_complexity} (步骤数: {step_count})")
            
        # 强制输出当前分析结果用于调试
        print(f"🔍 [DEBUG] 最终分析结果 - 复杂度: {analysis.get('complexity')}")
        print(f"🔍 [DEBUG] 所有步骤: {all_steps}")
        
        return analysis