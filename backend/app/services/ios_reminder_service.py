"""
iOS提醒服务
用于在iOS设备上设置本地通知和提醒
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from ..models.enhanced_models import Reminder, Item
from ..models.conversation_models import ReminderSchedule


class IOSReminderService:
    """iOS提醒服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_ios_reminder(
        self, 
        item_id: int, 
        user_id: int, 
        remind_time: datetime, 
        title: str, 
        message: str = None,
        repeat_type: str = None
    ) -> Dict[str, Any]:
        """
        创建iOS本地提醒
        
        Args:
            item_id: 关联的任务ID
            user_id: 用户ID
            remind_time: 提醒时间
            title: 提醒标题
            message: 提醒内容
            repeat_type: 重复类型 (none, daily, weekly, monthly)
        
        Returns:
            包含提醒信息的字典，用于前端设置iOS本地通知
        """
        
        # 生成唯一的通知ID
        notification_id = str(uuid.uuid4())
        
        # 创建基础提醒记录
        reminder = Reminder(
            item_id=item_id,
            user_id=user_id,
            remind_time=remind_time,
            title=title,
            message=message or title,
            repeat_type=repeat_type or "none",
            is_sent=False,
            is_read=False
        )
        
        self.db.add(reminder)
        self.db.flush()  # 获取ID
        
        # 如果有重复规则，创建调度记录
        if repeat_type and repeat_type != "none":
            schedule_config = self._create_schedule_config(repeat_type, remind_time)
            
            reminder_schedule = ReminderSchedule(
                item_id=item_id,
                user_id=user_id,
                schedule_type=repeat_type,
                schedule_config=schedule_config,
                next_remind_time=remind_time,
                ios_notification_id=notification_id,
                is_active=True
            )
            
            self.db.add(reminder_schedule)
        
        self.db.commit()
        
        # 返回iOS通知配置
        ios_notification = {
            "id": notification_id,
            "title": title,
            "body": message or title,
            "trigger": {
                "type": "calendar",
                "dateComponents": self._datetime_to_components(remind_time)
            },
            "sound": "default",
            "badge": 1
        }
        
        # 如果有重复，添加重复配置
        if repeat_type and repeat_type != "none":
            ios_notification["trigger"]["repeats"] = True
            ios_notification["trigger"]["repeatInterval"] = self._get_repeat_interval(repeat_type)
        
        return {
            "reminder_id": reminder.id,
            "notification_id": notification_id,
            "ios_notification": ios_notification,
            "success": True
        }
    
    def _create_schedule_config(self, repeat_type: str, base_time: datetime) -> Dict[str, Any]:
        """创建调度配置"""
        config = {
            "repeat_type": repeat_type,
            "base_time": base_time.isoformat(),
            "timezone": "Asia/Shanghai"  # 默认时区
        }
        
        if repeat_type == "daily":
            config["interval_days"] = 1
        elif repeat_type == "weekly":
            config["interval_days"] = 7
            config["weekday"] = base_time.weekday()
        elif repeat_type == "monthly":
            config["day_of_month"] = base_time.day
        
        return config
    
    def _datetime_to_components(self, dt: datetime) -> Dict[str, int]:
        """将datetime转换为iOS DateComponents格式"""
        return {
            "year": dt.year,
            "month": dt.month,
            "day": dt.day,
            "hour": dt.hour,
            "minute": dt.minute,
            "second": dt.second
        }
    
    def _get_repeat_interval(self, repeat_type: str) -> str:
        """获取iOS重复间隔"""
        mapping = {
            "daily": "day",
            "weekly": "weekOfYear", 
            "monthly": "month"
        }
        return mapping.get(repeat_type, "day")
    
    def parse_time_expression(self, text: str, base_time: datetime = None) -> Optional[datetime]:
        """
        解析时间表达式
        
        Args:
            text: 时间表达式，如"明天下午3点"、"下周一上午9点"
            base_time: 基准时间，默认为当前时间
        
        Returns:
            解析后的datetime对象
        """
        if base_time is None:
            base_time = datetime.now()
        
        text = text.lower().strip()
        
        # 简单的时间解析逻辑
        target_time = base_time
        
        # 解析日期部分
        if "明天" in text:
            target_time = target_time + timedelta(days=1)
        elif "后天" in text:
            target_time = target_time + timedelta(days=2)
        elif "下周" in text:
            target_time = target_time + timedelta(days=7)
        elif "下个月" in text:
            # 简单处理，加30天
            target_time = target_time + timedelta(days=30)
        
        # 解析时间部分
        hour = target_time.hour
        minute = target_time.minute
        
        if "上午" in text:
            if "9点" in text or "九点" in text:
                hour = 9
            elif "10点" in text or "十点" in text:
                hour = 10
            elif "11点" in text or "十一点" in text:
                hour = 11
            minute = 0
        elif "下午" in text:
            if "1点" in text or "一点" in text:
                hour = 13
            elif "2点" in text or "两点" in text or "二点" in text:
                hour = 14
            elif "3点" in text or "三点" in text:
                hour = 15
            elif "4点" in text or "四点" in text:
                hour = 16
            elif "5点" in text or "五点" in text:
                hour = 17
            elif "6点" in text or "六点" in text:
                hour = 18
            minute = 0
        elif "晚上" in text:
            if "7点" in text or "七点" in text:
                hour = 19
            elif "8点" in text or "八点" in text:
                hour = 20
            elif "9点" in text or "九点" in text:
                hour = 21
            minute = 0
        
        # 处理具体时间格式，如"15:30"
        import re
        time_pattern = r'(\d{1,2}):(\d{2})'
        time_match = re.search(time_pattern, text)
        if time_match:
            hour = int(time_match.group(1))
            minute = int(time_match.group(2))
        
        # 处理"点半"
        if "点半" in text:
            minute = 30
        
        return target_time.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    def get_reminder_status(self, notification_id: str) -> Dict[str, Any]:
        """获取提醒状态"""
        schedule = self.db.query(ReminderSchedule).filter(
            ReminderSchedule.ios_notification_id == notification_id
        ).first()
        
        if not schedule:
            return {"found": False}
        
        return {
            "found": True,
            "is_active": schedule.is_active,
            "next_remind_time": schedule.next_remind_time.isoformat() if schedule.next_remind_time else None,
            "schedule_type": schedule.schedule_type
        }
    
    def cancel_reminder(self, notification_id: str) -> bool:
        """取消提醒"""
        schedule = self.db.query(ReminderSchedule).filter(
            ReminderSchedule.ios_notification_id == notification_id
        ).first()
        
        if schedule:
            schedule.is_active = False
            self.db.commit()
            return True
        
        return False