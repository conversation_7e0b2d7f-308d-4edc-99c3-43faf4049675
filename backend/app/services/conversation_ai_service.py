import httpx
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dateutil import parser
import pytz
import re
from app.core.config import settings
from app.models.enhanced_schemas import AIAnalysisResult, TaskComplexity, MessageRole

class ConversationAIService:
    """对话式AI服务 - 处理完整的对话流程和任务创建"""
    
    def __init__(self):
        self.api_key = settings.QIANWEN_API_KEY
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.model = "qwen-turbo"
        
        # 系统提示词 - 智能对话策略
        self.system_prompt = """你是一个智能任务助手，专门帮助用户通过对话创建和管理任务。

【对话策略 - 核心原则】
1. **信息收集优先**：当用户意图不够明确时，优先通过提问收集信息
2. **渐进式确认**：复杂任务需要多轮对话确认细节后再创建
3. **上下文感知**：充分利用对话历史，理解用户的真实意图

【意图识别规则】
- **clarify（需要澄清）**：用户输入模糊，需要更多信息
  例如："我想组织活动"、"帮我做个计划"

- **create_task（创建简单任务）**：意图明确的单一操作
  例如："明天3点提醒我开会"、"买一瓶牛奶"

- **create_project（创建复杂项目）**：意图明确且信息充分的复杂任务
  例如："组织公司年会，时间是下个月15号，地点在会议中心，需要准备..."

- **modify_task（修改任务）**：基于已有任务的修改请求
- **query_task（查询任务）**：询问已有任务的详情

【复杂度判断】
- **SIMPLE**：单一操作，信息明确，可立即执行
- **COMPLEX**：多步骤项目，需要详细规划

【澄清策略】
当遇到模糊输入时，应该：
1. 识别为"clarify"意图
2. 提出2-3个具体问题收集关键信息
3. 不要立即创建任务，而是引导用户提供更多细节

【任务分类】运动、健康、工作、学习、生活、娱乐、购物、会议

请根据用户输入和对话上下文，智能判断最合适的处理方式。"""

    async def analyze_conversation(
        self,
        user_input: str,
        conversation_history: List[Dict],
        timezone: str = "Asia/Shanghai"
    ) -> AIAnalysisResult:
        """分析用户输入并返回处理结果 - 增强版，支持复杂任务分解"""

        try:
            # 构建对话上下文
            messages = self._build_conversation_context(user_input, conversation_history, timezone)

            # 调用AI模型
            ai_response = await self._call_ai_model(messages)

            # 解析AI回复
            analysis = self._parse_ai_response(ai_response, timezone)

            # 增强复杂任务处理
            if analysis.complexity == TaskComplexity.COMPLEX:
                analysis = await self._enhance_complex_task(analysis, user_input, conversation_history, timezone)

            return analysis

        except Exception as e:
            print(f"AI分析失败: {e}")
            return self._fallback_analysis(user_input, timezone)

    async def _enhance_complex_task(
        self,
        initial_analysis: AIAnalysisResult,
        user_input: str,
        conversation_history: List[Dict],
        timezone: str
    ) -> AIAnalysisResult:
        """增强复杂任务的分析，提供更详细的步骤分解"""

        # 如果已经有足够的步骤，直接返回
        if len(initial_analysis.suggested_steps) >= 5:
            return initial_analysis

        # 构建专门用于任务分解的提示词
        task_title = initial_analysis.title or "未命名任务"
        task_desc = initial_analysis.description or user_input

        task_prompt = f"""请为以下复杂任务提供详细的步骤分解：

任务标题：{task_title}
任务描述：{task_desc}

请提供至少6-10个具体步骤，每个步骤应该清晰、可执行，并按照时间顺序排列。
如果是组织活动类任务（如钓鱼比赛），请考虑准备阶段、执行阶段和收尾阶段。

返回格式：
{{
    "task_title": "任务标题",
    "task_description": "详细描述",
    "steps": [
        {{
            "title": "步骤1标题",
            "description": "步骤1详细描述",
            "estimated_duration": "预计时长（小时）"
        }},
        {{
            "title": "步骤2标题",
            "description": "步骤2详细描述",
            "estimated_duration": "预计时长（小时）"
        }}
        // 更多步骤...
    ]
}}
"""

        # 调用AI模型进行任务分解
        try:
            messages = [{"role": "system", "content": task_prompt}]
            task_analysis = await self._call_ai_model(messages)

            # 解析任务分解结果
            import json
            import re

            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', task_analysis, re.DOTALL)
            if json_match:
                task_data = json.loads(json_match.group())

                # 提取步骤
                detailed_steps = []
                if "steps" in task_data and isinstance(task_data["steps"], list):
                    for step in task_data["steps"]:
                        if isinstance(step, dict) and "title" in step:
                            detailed_steps.append(step["title"])
                        elif isinstance(step, str):
                            detailed_steps.append(step)

                # 更新分析结果
                if detailed_steps:
                    initial_analysis.suggested_steps = detailed_steps

                # 可能更新标题和描述
                if "task_title" in task_data and task_data["task_title"]:
                    initial_analysis.title = task_data["task_title"]
                if "task_description" in task_data and task_data["task_description"]:
                    initial_analysis.description = task_data["task_description"]

        except Exception as e:
            print(f"增强任务分解失败: {e}")
            # 失败时保持原始分析结果不变

        return initial_analysis
    
    def _build_conversation_context(
        self,
        user_input: str,
        history: List[Dict],
        timezone: str
    ) -> List[Dict]:
        """构建对话上下文 - 增强版，支持完整的上下文记忆"""

        current_time = datetime.now(pytz.timezone(timezone))

        # 分析对话历史，提取关键信息
        context_summary = self._extract_context_summary(history)

        # 系统消息 - 增强版
        system_content = f"""{self.system_prompt}

当前时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}
时区：{timezone}

【对话上下文摘要】
{context_summary}

【重要指令】
1. 充分利用上下文信息，理解用户的真实意图
2. 如果用户在修改之前讨论的任务，请识别为"modify_task"意图
3. 如果用户在询问之前创建的任务详情，请识别为"query_task"意图
4. 对于复杂任务，必须提供详细的步骤分解
5. 保持对话的连贯性和上下文一致性

返回格式：
{{
    "intent": "clarify|create_task|create_project|modify_task|query_task|confirm|complete_task",
    "category": "运动|健康|工作|学习|生活|娱乐|购物|会议",
    "title": "任务标题（仅在create_task/create_project时提供）",
    "description": "详细描述",
    "complexity": "SIMPLE|COMPLEX",
    "confidence": 0.9,
    "has_time": true/false,
    "time_expression": "明天下午3点",
    "absolute_time": "2024-01-01T15:00:00",
    "suggested_steps": ["步骤1", "步骤2", "步骤3"],
    "response_message": "AI回复给用户的消息",
    "clarification_questions": ["需要确认的问题1", "问题2"],
    "context_reference": "引用的上下文信息",
    "modification_target": "如果是修改意图，指明修改的目标",
    "information_needed": ["需要收集的信息类型1", "信息类型2"]
}}
"""

        messages = [{"role": "system", "content": system_content}]

        # 添加完整的历史对话（最近10条，保持更多上下文）
        recent_history = history[-10:] if len(history) > 10 else history
        for msg in recent_history:
            role = msg.get("role", "user").lower()
            if role == "assistant":
                role = "assistant"
            elif role == "user":
                role = "user"
            else:
                continue

            messages.append({
                "role": role,
                "content": msg.get("content", "")
            })

        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})

        return messages

    def _extract_context_summary(self, history: List[Dict]) -> str:
        """从对话历史中提取上下文摘要"""
        if not history:
            return "这是一个新的对话。"

        # 提取关键信息
        recent_topics = []
        mentioned_tasks = []

        for msg in history[-5:]:  # 分析最近5条消息
            content = msg.get("content", "")
            role = msg.get("role", "")

            if role.upper() == "USER":
                # 提取用户提到的关键词
                if any(keyword in content for keyword in ["钓鱼", "比赛", "活动", "项目", "计划", "步骤"]):
                    recent_topics.append(content[:50] + "...")

            elif role.upper() == "ASSISTANT":
                # 提取AI创建的任务信息
                if "已为您创建任务" in content:
                    task_name = content.replace("已为您创建任务：", "").strip()
                    mentioned_tasks.append(task_name)

        summary_parts = []
        if recent_topics:
            summary_parts.append(f"最近讨论的话题：{'; '.join(recent_topics)}")
        if mentioned_tasks:
            summary_parts.append(f"已创建的任务：{'; '.join(mentioned_tasks)}")

        if summary_parts:
            return "\n".join(summary_parts)
        else:
            return "对话刚开始，暂无特定上下文。"
    
    async def _call_ai_model(self, messages: List[Dict]) -> str:
        """调用通义千问API - 忽略系统代理"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "input": {"messages": messages},
            "parameters": {
                "max_tokens": 2000,
                "temperature": 0.3,  # 降低随机性，提高一致性
                "top_p": 0.8
            }
        }
        
        # 创建HTTP客户端
        async with httpx.AsyncClient(
            timeout=30.0
        ) as client:
            response = await client.post(
                self.base_url,
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["output"]["text"]
            else:
                raise Exception(f"API调用失败: {response.status_code}")
    
    def _parse_ai_response(self, ai_response: str, timezone: str) -> AIAnalysisResult:
        """解析AI返回的JSON结果"""
        
        try:
            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
            else:
                # 如果没有找到JSON，解析纯文本
                return self._parse_text_response(ai_response, timezone)
            
            # 解析时间
            absolute_time = None
            if data.get("has_time") and data.get("absolute_time"):
                try:
                    absolute_time = parser.parse(data["absolute_time"])
                    if absolute_time.tzinfo is None:
                        tz = pytz.timezone(timezone)
                        absolute_time = tz.localize(absolute_time)
                except:
                    absolute_time = self._parse_natural_time(
                        data.get("time_expression", ""), timezone
                    )
            
            # 处理复杂度值的大小写转换
            complexity_value = data.get("complexity", "simple").upper()
            if complexity_value not in ["SIMPLE", "COMPLEX"]:
                complexity_value = "SIMPLE"
            
            return AIAnalysisResult(
                intent=data.get("intent", "create_task"),
                category=data.get("category"),
                title=data.get("title", ""),
                description=data.get("description", ""),
                complexity=TaskComplexity(complexity_value),
                confidence=float(data.get("confidence", 0.8)),
                has_time=data.get("has_time", False),
                time_expression=data.get("time_expression"),
                absolute_time=absolute_time,
                suggested_steps=data.get("suggested_steps", []),
                response_message=data.get("response_message", "我来帮您处理这个任务。"),
                clarification_questions=data.get("clarification_questions", []),
                context_reference=data.get("context_reference", ""),
                modification_target=data.get("modification_target", "")
            )
            
        except Exception as e:
            print(f"解析AI回复失败: {e}")
            return self._parse_text_response(ai_response, timezone)
    
    def _parse_text_response(self, response: str, timezone: str) -> AIAnalysisResult:
        """解析纯文本AI回复"""
        
        # 简单的关键词分析
        intent = "create_task"
        category = "生活"
        complexity = TaskComplexity.SIMPLE
        
        # 判断意图
        if any(word in response for word in ["项目", "开发", "计划", "设计"]):
            intent = "create_project"
            complexity = TaskComplexity.COMPLEX
        elif any(word in response for word in ["提醒", "记住", "别忘"]):
            intent = "set_reminder"
        
        # 判断分类
        if any(word in response for word in ["运动", "健身", "跑步", "游泳"]):
            category = "运动"
        elif any(word in response for word in ["工作", "会议", "开发", "项目"]):
            category = "工作"
        elif any(word in response for word in ["学习", "读书", "课程"]):
            category = "学习"
        elif any(word in response for word in ["健康", "医院", "吃药"]):
            category = "健康"
        
        return AIAnalysisResult(
            intent=intent,
            category=category,
            title=response[:50],
            description=response,
            complexity=complexity,
            confidence=0.6,
            has_time=False,
            response_message="我理解了您的需求，让我来帮您处理。",
            clarification_questions=[]
        )
    
    def _parse_natural_time(self, time_expr: str, timezone: str) -> Optional[datetime]:
        """解析自然语言时间表达"""
        
        if not time_expr:
            return None
            
        try:
            tz = pytz.timezone(timezone)
            now = datetime.now(tz)
            
            # 处理相对时间
            if "明天" in time_expr:
                base_date = now + timedelta(days=1)
            elif "后天" in time_expr:
                base_date = now + timedelta(days=2)
            elif "下周" in time_expr:
                base_date = now + timedelta(days=7)
            else:
                base_date = now
            
            # 处理具体时间
            hour = base_date.hour
            minute = 0
            
            # 提取小时
            hour_match = re.search(r'(\d+)[点时]', time_expr)
            if hour_match:
                hour = int(hour_match.group(1))
                if "下午" in time_expr and hour < 12:
                    hour += 12
                elif "上午" in time_expr and hour == 12:
                    hour = 0
            elif "上午" in time_expr:
                hour = 9
            elif "下午" in time_expr:
                hour = 14
            elif "晚上" in time_expr:
                hour = 19
            
            # 提取分钟
            minute_match = re.search(r'(\d+)分', time_expr)
            if minute_match:
                minute = int(minute_match.group(1))
            
            result = base_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return result
            
        except Exception as e:
            print(f"时间解析失败: {e}")
            return None
    
    def _fallback_analysis(self, user_input: str, timezone: str) -> AIAnalysisResult:
        """AI调用失败时的备用分析"""
        
        return AIAnalysisResult(
            intent="create_task",
            category="生活",
            title=user_input[:30],
            description=user_input,
            complexity=TaskComplexity.SIMPLE,
            confidence=0.3,
            has_time=False,
            response_message="抱歉，AI服务暂时不可用。我会用基础方式为您创建这个任务。",
            clarification_questions=[]
        )