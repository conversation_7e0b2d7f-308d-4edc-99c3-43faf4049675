import httpx
import json
from datetime import datetime, timedelta
from typing import Dict, Any
from dateutil import parser
import pytz
from app.core.config import settings
from app.models.schemas import AIAnalysisResponse
from app.ai.model_client import AIModelClient

class AIService:
    """AI服务类，处理文本分析和意图识别"""
    
    def __init__(self):
        self.categories = {
            "运动": {"icon": "🏃", "color": "#FF6B6B"},
            "健康": {"icon": "🏥", "color": "#4ECDC4"},
            "工作": {"icon": "💼", "color": "#45B7D1"},
            "学习": {"icon": "📚", "color": "#96CEB4"},
            "生活": {"icon": "🏠", "color": "#FECA57"},
            "娱乐": {"icon": "🎮", "color": "#FF9FF3"},
            "购物": {"icon": "🛒", "color": "#54A0FF"},
            "会议": {"icon": "👥", "color": "#5F27CD"}
        }
        
        # 初始化AI客户端
        self.ai_client = AIModelClient(provider="qianwen")  # 可以根据需要切换
    
    async def analyze_user_input(self, text: str, timezone: str = "Asia/Shanghai") -> Dict[str, Any]:
        """
        分析用户输入文本，提取分类、时间、任务等信息
        """
        try:
            # 获取当前时间
            tz = pytz.timezone(timezone)
            current_time = datetime.now(tz).strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"🤖 [AI] 开始调用通义千问API分析用户输入: {text}")
            
            # 使用AI模型分析文本
            analysis_result = await self.ai_client.analyze_text(
                text=text,
                current_time=current_time,
                timezone=timezone
            )
            
            print(f"🤖 [AI] 通义千问API返回结果: {analysis_result}")
            
            # 处理AI返回的结果
            if analysis_result and isinstance(analysis_result, dict):
                # 构建标准化的返回格式
                return {
                    'analysis': {
                        'intent': 'task_creation',
                        'category': analysis_result.get('category', '生活'),
                        'title': analysis_result.get('title', text[:30]),
                        'description': analysis_result.get('description', text),
                        'content': analysis_result.get('content', {}),
                        'complexity': 'SIMPLE',
                        'priority': 'medium',
                        'confidence': analysis_result.get('confidence', 0.8),
                        'has_time': analysis_result.get('has_time', False),
                        'remind_time': analysis_result.get('absolute_time')
                    },
                    'response': f"已为您创建任务：{analysis_result.get('title', text[:30])}",
                    'reminders': self._build_reminders(analysis_result, text) if analysis_result.get('has_time') else []
                }
            else:
                print(f"🤖 [AI] AI返回结果格式异常，使用备用分析")
                # AI返回格式异常，使用备用分析
                fallback_result = self._fallback_analysis(text, timezone)
            
            # 返回字典格式，包含analysis键
            return {
                'analysis': {
                    'intent': 'task_creation',
                    'category': fallback_result.category,
                    'title': fallback_result.title,
                    'description': fallback_result.description,
                    'content': fallback_result.content,
                    'complexity': 'SIMPLE',
                    'priority': 'medium',
                    'confidence': fallback_result.confidence,
                    'has_time': len(fallback_result.reminders) > 0,
                    'remind_time': fallback_result.reminders[0]['remind_time'] if fallback_result.reminders else None
                },
                'response': f"已为您创建任务：{fallback_result.title}",
                'reminders': fallback_result.reminders
            }
            
        except Exception as e:
            print(f"🤖 [AI] AI分析失败: {e}")
            # 如果AI调用失败，使用简单的规则分析
            fallback_result = self._fallback_analysis(text, timezone)
            return {
                'analysis': {
                    'intent': 'task_creation',
                    'category': fallback_result.category,
                    'title': fallback_result.title,
                    'description': fallback_result.description,
                    'content': fallback_result.content,
                    'complexity': 'SIMPLE',
                    'priority': 'medium',
                    'confidence': fallback_result.confidence,
                    'has_time': len(fallback_result.reminders) > 0,
                    'remind_time': fallback_result.reminders[0]['remind_time'] if fallback_result.reminders else None
                },
                'response': f"已为您创建任务：{fallback_result.title}",
                'reminders': fallback_result.reminders
            }

    def _build_reminders(self, analysis_result: Dict[str, Any], text: str) -> list:
        """根据AI分析结果构建提醒信息"""
        reminders = []
        if analysis_result.get('has_time') and analysis_result.get('absolute_time'):
            reminders.append({
                "remind_time": analysis_result['absolute_time'],
                "title": f"提醒：{analysis_result.get('title', text[:20])}",
                "message": analysis_result.get('description', text)
            })
        return reminders
    
    def analyze_with_context(self, text: str, context_prompt: str, context_data: Dict[str, Any], timezone: str = "Asia/Shanghai") -> Dict[str, Any]:
        """
        基于上下文分析用户输入
        """
        try:
            # 获取当前时间
            tz = pytz.timezone(timezone)
            current_time = datetime.now(tz).strftime('%Y-%m-%d %H:%M:%S')
            
            # 构建包含上下文的完整prompt
            full_text = f"{context_prompt}\n\n用户输入：{text}"
            
            # 暂时使用简单的响应，避免异步调用问题
            # analysis_result = await self.ai_client.analyze_text(
            #     text=full_text,
            #     current_time=current_time,
            #     timezone=timezone
            # )
            
            # 返回包含分析结果的字典
            return {
                'analysis': {
                    'intent': 'context_continuation',
                    'response': f"我理解您的输入：{text}，请继续提供更多信息。",
                    'confidence': 0.7,
                    'context_update': {'last_input': text}
                },
                'response': f"我理解您的输入：{text}，请继续提供更多信息。",
                'prompt': context_prompt
            }
            
        except Exception as e:
            print(f"上下文AI分析失败: {e}")
            # 如果AI调用失败，返回简单的响应
            return {
                'analysis': {
                    'intent': 'context_continuation',
                    'response': f"我理解您的输入：{text}，请继续提供更多信息。",
                    'confidence': 0.5
                },
                'response': f"我理解您的输入：{text}，请继续提供更多信息。",
                'prompt': context_prompt
            }
    
    def _parse_absolute_time(self, time_str: str, timezone: str) -> datetime:
        """解析绝对时间字符串"""
        try:
            tz = pytz.timezone(timezone)
            # 解析时间字符串
            dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            # 添加时区信息
            return tz.localize(dt)
        except Exception as e:
            print(f"时间解析失败: {e}")
            return None
    
    def _fallback_analysis(self, text: str, timezone: str) -> AIAnalysisResponse:
        """AI调用失败时的备用分析"""
        
        # 简单的关键词匹配
        if any(word in text for word in ["羽毛球", "跑步", "健身", "游泳", "运动"]):
            category = "运动"
        elif any(word in text for word in ["医院", "体检", "吃药", "健康"]):
            category = "健康"
        elif any(word in text for word in ["开会", "会议", "讨论"]):
            category = "会议"
        elif any(word in text for word in ["项目", "开发", "编程", "网页"]):
            category = "工作"
        else:
            category = "生活"
        
        # 简单的时间解析
        reminders = []
        if any(word in text for word in ["明天", "后天", "下周", "点", "时"]):
            # 简单估算提醒时间
            tz = pytz.timezone(timezone)
            base_time = datetime.now(tz)
            
            if "明天" in text:
                remind_time = base_time + timedelta(days=1)
                remind_time = remind_time.replace(hour=9, minute=0, second=0)
            elif "后天" in text:
                remind_time = base_time + timedelta(days=2) 
                remind_time = remind_time.replace(hour=9, minute=0, second=0)
            else:
                remind_time = base_time + timedelta(hours=1)
            
            reminders.append({
                "remind_time": remind_time.isoformat(),
                "title": f"提醒：{text[:20]}",
                "message": text
            })
        
        return AIAnalysisResponse(
            category=category,
            title=text[:30] + "..." if len(text) > 30 else text,
            description=f"基于关键词匹配分析：{text}",
            content={"details": "使用备用分析方法"},
            reminders=reminders,
            confidence=0.5
        )