import requests
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dateutil import parser
import pytz
import re
from app.core.config import settings
from app.models.enhanced_schemas import AIAnalysisResult, TaskComplexity, MessageRole

class ConversationAIService:
    """对话式AI服务 - 处理完整的对话流程和任务创建"""
    
    def __init__(self):
        self.api_key = settings.QIANWEN_API_KEY
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.model = "qwen-turbo"
        
        # 系统提示词
        self.system_prompt = """你是一个智能任务助手，专门帮助用户创建和管理任务。你的职责包括：

1. 理解用户的意图并分类处理：
   - 创建简单任务（如："明天3点开会"）
   - 创建复杂项目（如："开发一个网站"）
   - 设置提醒（如："提醒我吃药"）
   - 查询或修改现有任务

2. 对话流程：
   - 简单任务：直接确认并创建
   - 复杂任务：询问详细信息，分解步骤
   - 不明确的输入：询问澄清问题

3. 任务分类：运动、健康、工作、学习、生活、娱乐、购物、会议

4. 时间解析：将自然语言时间转换为具体时间

请根据用户输入返回JSON格式的分析结果。"""

    async def analyze_conversation(
        self, 
        user_input: str, 
        conversation_history: List[Dict], 
        timezone: str = "Asia/Shanghai"
    ) -> AIAnalysisResult:
        """分析用户输入并返回处理结果"""
        
        try:
            # 构建对话上下文
            messages = self._build_conversation_context(user_input, conversation_history, timezone)
            
            # 调用AI模型
            ai_response = await self._call_ai_model(messages)
            
            # 解析AI回复
            analysis = self._parse_ai_response(ai_response, timezone)
            
            return analysis
            
        except Exception as e:
            print(f"AI分析失败: {e}")
            return self._fallback_analysis(user_input, timezone)
    
    def _build_conversation_context(
        self, 
        user_input: str, 
        history: List[Dict], 
        timezone: str
    ) -> List[Dict]:
        """构建对话上下文"""
        
        current_time = datetime.now(pytz.timezone(timezone))
        
        # 系统消息
        system_content = f"""{self.system_prompt}

当前时间：{current_time.strftime('%Y-%m-%d %H:%M:%S')}
时区：{timezone}

返回格式：
{{
    "intent": "create_task|create_project|set_reminder|clarify|confirm|query|complete_task",
    "category": "运动|健康|工作|学习|生活|娱乐|购物|会议",
    "title": "任务标题",
    "description": "详细描述",
    "complexity": "simple|complex",
    "confidence": 0.9,
    "has_time": true/false,
    "time_expression": "明天下午3点",
    "absolute_time": "2024-01-01T15:00:00",
    "suggested_steps": ["步骤1", "步骤2"],
    "response_message": "AI回复给用户的消息",
    "clarification_questions": ["需要确认的问题1", "问题2"]
}}
"""
        
        messages = [{"role": "system", "content": system_content}]
        
        # 添加历史对话（最近10条）
        recent_history = history[-10:] if len(history) > 10 else history
        for msg in recent_history:
            messages.append({
                "role": msg.get("role", "user"),
                "content": msg.get("content", "")
            })
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    async def _call_ai_model(self, messages: List[Dict]) -> str:
        """调用通义千问API - 使用requests库避免代理问题"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "input": {"messages": messages},
            "parameters": {
                "max_tokens": 2000,
                "temperature": 0.3,
                "top_p": 0.8
            }
        }
        
        # 使用requests.Session并禁用代理
        session = requests.Session()
        session.trust_env = False  # 不信任环境变量
        session.proxies = {}  # 不使用代理
        
        try:
            response = session.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["output"]["text"]
            else:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")
        finally:
            session.close()
    
    def _parse_ai_response(self, ai_response: str, timezone: str) -> AIAnalysisResult:
        """解析AI返回的JSON结果"""
        
        try:
            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
            else:
                # 如果没有找到JSON，解析纯文本
                return self._parse_text_response(ai_response, timezone)
            
            # 解析时间
            absolute_time = None
            if data.get("has_time") and data.get("absolute_time"):
                try:
                    absolute_time = parser.parse(data["absolute_time"])
                    if absolute_time.tzinfo is None:
                        tz = pytz.timezone(timezone)
                        absolute_time = tz.localize(absolute_time)
                except:
                    absolute_time = self._parse_natural_time(
                        data.get("time_expression", ""), timezone
                    )
            
            # 处理复杂度值的大小写转换
            complexity_value = data.get("complexity", "simple").upper()
            if complexity_value not in ["SIMPLE", "COMPLEX"]:
                complexity_value = "SIMPLE"
            
            return AIAnalysisResult(
                intent=data.get("intent", "create_task"),
                category=data.get("category"),
                title=data.get("title", ""),
                description=data.get("description", ""),
                complexity=TaskComplexity(complexity_value),
                confidence=float(data.get("confidence", 0.8)),
                has_time=data.get("has_time", False),
                time_expression=data.get("time_expression"),
                absolute_time=absolute_time,
                suggested_steps=data.get("suggested_steps", []),
                response_message=data.get("response_message", "我来帮您处理这个任务。"),
                clarification_questions=data.get("clarification_questions", [])
            )
            
        except Exception as e:
            print(f"解析AI回复失败: {e}")
            return self._parse_text_response(ai_response, timezone)
    
    def _parse_text_response(self, response: str, timezone: str) -> AIAnalysisResult:
        """解析纯文本AI回复"""
        
        # 智能关键词分析
        intent = "create_task"
        category = "生活"
        complexity = TaskComplexity.SIMPLE
        has_time = False
        time_expression = None
        absolute_time = None
        
        # 判断意图
        if any(word in response for word in ["项目", "开发", "计划", "设计", "网站", "应用"]):
            intent = "create_project"
            complexity = TaskComplexity.COMPLEX
        elif any(word in response for word in ["提醒", "记住", "别忘", "通知"]):
            intent = "set_reminder"
        
        # 判断分类
        if any(word in response for word in ["运动", "健身", "跑步", "游泳", "羽毛球", "篮球", "足球"]):
            category = "运动"
        elif any(word in response for word in ["工作", "会议", "开发", "项目", "任务"]):
            category = "工作"
        elif any(word in response for word in ["学习", "读书", "课程", "考试"]):
            category = "学习"
        elif any(word in response for word in ["健康", "医院", "吃药", "体检"]):
            category = "健康"
        elif any(word in response for word in ["购物", "买", "商店", "超市"]):
            category = "购物"
        elif any(word in response for word in ["娱乐", "电影", "游戏", "音乐"]):
            category = "娱乐"
        elif any(word in response for word in ["会议", "开会", "讨论", "商讨"]):
            category = "会议"
        
        # 解析时间
        time_patterns = [
            r'明天',
            r'后天', 
            r'下周',
            r'(\d+)[点时]',
            r'上午',
            r'下午',
            r'晚上',
            r'(\d+)月(\d+)[日号]',
        ]
        
        for pattern in time_patterns:
            if re.search(pattern, response):
                has_time = True
                time_expression = response
                absolute_time = self._parse_natural_time(response, timezone)
                break
        
        # 生成回复消息
        response_message = f"我理解了！您想要{intent}一个{category}相关的任务。"
        if has_time:
            response_message += "我已经为您解析了时间信息。"
        if complexity == TaskComplexity.COMPLEX:
            response_message += "这看起来是个复杂项目，我会为您分解步骤。"
        
        return AIAnalysisResult(
            intent=intent,
            category=category,
            title=response[:50],
            description=response,
            complexity=complexity,
            confidence=0.8,  # 提高备用解析的置信度
            has_time=has_time,
            time_expression=time_expression,
            absolute_time=absolute_time,
            response_message=response_message,
            clarification_questions=[]
        )
    
    def _parse_natural_time(self, time_expr: str, timezone: str) -> Optional[datetime]:
        """解析自然语言时间表达"""
        
        if not time_expr:
            return None
            
        try:
            tz = pytz.timezone(timezone)
            now = datetime.now(tz)
            
            # 处理相对时间
            if "明天" in time_expr:
                base_date = now + timedelta(days=1)
            elif "后天" in time_expr:
                base_date = now + timedelta(days=2)
            elif "下周" in time_expr:
                base_date = now + timedelta(days=7)
            else:
                base_date = now
            
            # 处理具体时间
            hour = base_date.hour
            minute = 0
            
            # 提取小时
            hour_match = re.search(r'(\d+)[点时]', time_expr)
            if hour_match:
                hour = int(hour_match.group(1))
                if "下午" in time_expr and hour < 12:
                    hour += 12
                elif "上午" in time_expr and hour == 12:
                    hour = 0
            elif "上午" in time_expr:
                hour = 9
            elif "下午" in time_expr:
                hour = 15  # 默认下午3点
            elif "晚上" in time_expr:
                hour = 19
            
            # 提取分钟
            minute_match = re.search(r'(\d+)分', time_expr)
            if minute_match:
                minute = int(minute_match.group(1))
            
            result = base_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
            return result
            
        except Exception as e:
            print(f"时间解析失败: {e}")
            return None
    
    def _fallback_analysis(self, user_input: str, timezone: str) -> AIAnalysisResult:
        """AI调用失败时的备用分析"""
        return self._parse_text_response(user_input, timezone)