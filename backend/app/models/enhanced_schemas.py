from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List, Any
from enum import Enum

class MessageRole(str, Enum):
    USER = "USER"
    ASSISTANT = "ASSISTANT"
    SYSTEM = "SYSTEM"

class ConversationStatus(str, Enum):
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"

class TaskStatus(str, Enum):
    DRAFT = "DRAFT"
    CONFIRMED = "CONFIRMED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"

class TaskComplexity(str, Enum):
    SIMPLE = "SIMPLE"
    COMPLEX = "COMPLEX"

# 对话相关的Schema
class ConversationMessageCreate(BaseModel):
    content: str
    is_voice_input: bool = False

class ConversationMessage(BaseModel):
    id: int
    role: MessageRole
    content: str
    ai_analysis: Optional[dict] = None
    timestamp: datetime
    is_voice_input: bool = False
    
    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    title: Optional[str] = None

class Conversation(BaseModel):
    id: int
    title: str
    status: ConversationStatus
    created_at: datetime
    updated_at: datetime
    result_item_id: Optional[int] = None
    messages: List[ConversationMessage] = []
    
    class Config:
        from_attributes = True

# AI分析相关的Schema
class AIAnalysisResult(BaseModel):
    intent: str  # 用户意图：create_task, modify_task, query_task, clarify, confirm, cancel等
    category: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    complexity: TaskComplexity
    confidence: float

    # 时间相关
    has_time: bool = False
    time_expression: Optional[str] = None
    absolute_time: Optional[datetime] = None

    # 任务分解（复杂任务）
    suggested_steps: List[str] = []

    # AI的回复内容
    response_message: str

    # 需要进一步确认的问题
    clarification_questions: List[str] = []

    # 上下文相关（新增）
    context_reference: Optional[str] = None  # 引用的上下文信息
    modification_target: Optional[str] = None  # 修改的目标任务
    information_needed: List[str] = []  # 需要收集的信息类型

class ConversationInput(BaseModel):
    conversation_id: Optional[int] = None
    message: str
    is_voice_input: bool = False
    user_timezone: str = "Asia/Shanghai"

class ConversationResponse(BaseModel):
    conversation_id: int
    message: ConversationMessage
    ai_response: ConversationMessage
    analysis: AIAnalysisResult
    
    # 如果创建了任务
    created_item: Optional[dict] = None

# 任务相关的Schema
class TaskStepCreate(BaseModel):
    title: str
    description: Optional[str] = None
    parent_step_id: Optional[int] = None
    level: int = 1
    estimated_duration: Optional[int] = None
    step_type: str = "execution"
    dependencies: List[int] = []
    step_metadata: Optional[dict] = None

class TaskStep(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    parent_step_id: Optional[int] = None
    order_index: int
    level: int
    status: TaskStatus
    estimated_duration: Optional[int] = None
    step_type: str
    dependencies: List[int] = []
    step_metadata: Optional[dict] = None
    sub_steps: List['TaskStep'] = []  # 子步骤

    class Config:
        from_attributes = True

# 解决前向引用问题
TaskStep.model_rebuild()

class ItemCreate(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[dict] = None
    category_id: int
    complexity: TaskComplexity = TaskComplexity.SIMPLE
    priority: str = "medium"
    due_date: Optional[datetime] = None
    steps: List[TaskStepCreate] = []

class ItemUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[str] = None
    due_date: Optional[datetime] = None

class Item(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    content: Optional[dict] = None
    complexity: TaskComplexity
    status: TaskStatus
    priority: str
    ai_confidence: int
    created_at: datetime
    updated_at: datetime
    due_date: Optional[datetime] = None
    category: Optional[dict] = None
    steps: List[TaskStep] = []
    
    class Config:
        from_attributes = True

# 用户相关Schema保持不变
class UserBase(BaseModel):
    username: str
    email: str

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class CategoryBase(BaseModel):
    name: str
    icon: Optional[str] = None
    color: Optional[str] = None
    description: Optional[str] = None

class Category(CategoryBase):
    id: int
    
    class Config:
        from_attributes = True