from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Foreign<PERSON>ey, JSON, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from ..core.timezone_utils import get_local_now_naive

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=get_local_now_naive)
    
    conversations = relationship("Conversation", back_populates="user")
    items = relationship("Item", back_populates="owner")

class ConversationStatus(enum.Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class Conversation(Base):
    """对话会话表 - 记录用户与AI的完整对话"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String)  # 对话标题，根据第一条消息生成
    status = Column(Enum(ConversationStatus), default=ConversationStatus.ACTIVE)
    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)
    
    # 对话结果
    result_item_id = Column(Integer, ForeignKey("items.id"), nullable=True)
    
    user = relationship("User", back_populates="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation")
    result_item = relationship("Item")

class MessageRole(enum.Enum):
    USER = "user"
    ASSISTANT = "assistant" 
    SYSTEM = "system"

class ConversationMessage(Base):
    """对话消息表 - 记录每条对话消息"""
    __tablename__ = "conversation_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"))
    role = Column(Enum(MessageRole))
    content = Column(Text)
    
    # AI分析结果（如果是AI回复）
    ai_analysis = Column(JSON, nullable=True)
    
    # 消息元数据
    timestamp = Column(DateTime, default=get_local_now_naive)
    is_voice_input = Column(Boolean, default=False)
    
    conversation = relationship("Conversation", back_populates="messages")

class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    icon = Column(String)
    color = Column(String)
    description = Column(Text)
    
    items = relationship("Item", back_populates="category")

class TaskStatus(enum.Enum):
    DRAFT = "DRAFT"           # 草稿，需要进一步确认
    CONFIRMED = "CONFIRMED"   # 已确认，待执行
    IN_PROGRESS = "IN_PROGRESS"  # 执行中
    COMPLETED = "COMPLETED"   # 已完成
    CANCELLED = "CANCELLED"   # 已取消

class TaskComplexity(enum.Enum):
    SIMPLE = "SIMPLE"        # 简单任务，可直接创建
    COMPLEX = "COMPLEX"      # 复杂任务，需要分步骤

class Item(Base):
    """通用项目模型 - 任务、项目、健康记录等"""
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(Text)
    content = Column(JSON)  # 存储结构化数据，如项目步骤等
    
    # 分类和用户关联
    category_id = Column(Integer, ForeignKey("categories.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 任务属性
    complexity = Column(Enum(TaskComplexity), default=TaskComplexity.SIMPLE)
    status = Column(Enum(TaskStatus), default=TaskStatus.DRAFT)
    priority = Column(String, default="medium")
    
    # AI置信度
    ai_confidence = Column(Integer, default=0)  # 0-100
    
    # 置顶状态
    is_pinned = Column(Boolean, default=False)
    
    # 时间相关
    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)
    due_date = Column(DateTime, nullable=True)
    
    # 关联
    category = relationship("Category", back_populates="items")
    owner = relationship("User", back_populates="items")
    reminders = relationship("Reminder", back_populates="item")
    steps = relationship("TaskStep", back_populates="item")

class TaskStep(Base):
    """任务步骤表 - 复杂任务的分解步骤，支持层级结构"""
    __tablename__ = "task_steps"

    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("items.id"))
    parent_step_id = Column(Integer, ForeignKey("task_steps.id"), nullable=True)  # 父步骤ID，支持子任务

    title = Column(String)
    description = Column(Text)
    order_index = Column(Integer)  # 步骤顺序
    level = Column(Integer, default=1)  # 层级：1=主步骤，2=子步骤，3=子子步骤

    status = Column(Enum(TaskStatus), default=TaskStatus.DRAFT)
    estimated_duration = Column(Integer)  # 预估时长（分钟）

    # 步骤类型：preparation(准备), execution(执行), review(检查), cleanup(收尾)
    step_type = Column(String, default="execution")

    # 依赖关系：需要完成的前置步骤ID列表（JSON格式）
    dependencies = Column(JSON, default=list)

    # 步骤元数据
    step_metadata = Column(JSON, default=dict)  # 存储额外信息，如资源需求、注意事项等

    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)

    # 关联关系
    item = relationship("Item", back_populates="steps")
    parent_step = relationship("TaskStep", remote_side=[id], backref="sub_steps")

class Reminder(Base):
    """提醒模型"""
    __tablename__ = "reminders"
    
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("items.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 提醒时间和内容
    remind_time = Column(DateTime)
    title = Column(String)
    message = Column(Text)
    
    # 提醒状态
    is_sent = Column(Boolean, default=False)
    is_read = Column(Boolean, default=False)
    
    # 重复设置
    repeat_type = Column(String)  # none, daily, weekly, monthly
    repeat_interval = Column(Integer, default=1)
    
    created_at = Column(DateTime, default=get_local_now_naive)
    
    # 关联
    item = relationship("Item", back_populates="reminders")
    user = relationship("User")