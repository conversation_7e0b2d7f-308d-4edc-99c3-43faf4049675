from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from ..core.timezone_utils import get_local_now_naive

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=get_local_now_naive)
    
    items = relationship("Item", back_populates="owner")

class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)  # 运动、健康、工作、学习等
    icon = Column(String)
    color = Column(String)
    description = Column(Text)
    
    items = relationship("Item", back_populates="category")

class Item(Base):
    """通用项目模型 - 可以是任务、项目、健康记录等"""
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(Text)
    content = Column(JSON)  # 存储结构化数据，如项目步骤等
    
    # 分类和用户关联
    category_id = Column(Integer, ForeignKey("categories.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 时间相关
    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)
    
    # 状态
    status = Column(String, default="active")  # active, completed, archived
    priority = Column(String, default="medium")  # high, medium, low
    
    # 关联
    category = relationship("Category", back_populates="items")
    owner = relationship("User", back_populates="items")
    reminders = relationship("Reminder", back_populates="item")

class Reminder(Base):
    """提醒模型 - 所有类别的项目都可以有提醒"""
    __tablename__ = "reminders"
    
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("items.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 提醒时间和内容
    remind_time = Column(DateTime)
    title = Column(String)
    message = Column(Text)
    
    # 提醒状态
    is_sent = Column(Boolean, default=False)
    is_read = Column(Boolean, default=False)
    
    # 重复设置
    repeat_type = Column(String)  # none, daily, weekly, monthly
    repeat_interval = Column(Integer, default=1)
    
    created_at = Column(DateTime, default=get_local_now_naive)
    
    # 关联
    item = relationship("Item", back_populates="reminders")
    user = relationship("User")