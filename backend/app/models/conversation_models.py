"""
对话相关的增强模型
解决多轮对话、上下文管理、AI输入输出记录等问题
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, JSON, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from ..core.timezone_utils import get_local_now_naive

# 使用主模型文件的Base
from .enhanced_models import Base

class ConversationContext(Base):
    """对话上下文表 - 管理对话的上下文信息"""
    __tablename__ = "conversation_contexts"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"))
    
    # 上下文类型：task_creation, reminder_setting, complex_planning
    context_type = Column(String)
    
    # 当前步骤（用于复杂任务的多步骤处理）
    current_step = Column(Integer, default=1)
    total_steps = Column(Integer, default=1)
    
    # 上下文数据（JSON格式存储临时数据）
    context_data = Column(JSON)
    
    # 是否需要用户确认
    needs_confirmation = Column(Boolean, default=False)
    
    # 上下文状态：active, waiting_confirmation, completed
    status = Column(String, default="active")
    
    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)

class AIInteraction(Base):
    """AI交互记录表 - 详细记录AI的输入输出"""
    __tablename__ = "ai_interactions"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"))
    message_id = Column(Integer, ForeignKey("conversation_messages.id"), nullable=True)
    
    # 用户原始输入
    user_input = Column(Text)
    
    # AI处理过程记录
    ai_prompt = Column(Text)  # 发送给AI的完整prompt
    ai_raw_response = Column(Text)  # AI的原始回复
    ai_processed_response = Column(Text)  # 处理后的回复
    
    # 分析结果
    analysis_result = Column(JSON)
    
    # 处理时间统计
    processing_time_ms = Column(Integer)
    
    # 错误信息（如果有）
    error_message = Column(Text, nullable=True)
    
    created_at = Column(DateTime, default=get_local_now_naive)

class TaskTemplate(Base):
    """任务模板表 - 存储常见的复杂任务模板"""
    __tablename__ = "task_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)  # 如：减肥计划、学习计划
    category = Column(String)
    description = Column(Text)
    
    # 模板步骤（JSON格式）
    template_steps = Column(JSON)
    
    # 预估总时长（天）
    estimated_duration_days = Column(Integer)
    
    # 使用次数统计
    usage_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=get_local_now_naive)

class ReminderSchedule(Base):
    """提醒调度表 - 管理复杂的提醒规则"""
    __tablename__ = "reminder_schedules"
    
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(Integer, ForeignKey("items.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 提醒规则
    schedule_type = Column(String)  # daily, weekly, monthly, custom
    schedule_config = Column(JSON)  # 具体的调度配置
    
    # 下次提醒时间
    next_remind_time = Column(DateTime)
    
    # iOS集成相关
    ios_notification_id = Column(String, nullable=True)  # iOS本地通知ID
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=get_local_now_naive)
    updated_at = Column(DateTime, default=get_local_now_naive, onupdate=get_local_now_naive)