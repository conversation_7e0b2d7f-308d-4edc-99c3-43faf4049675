from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List, Any

class UserBase(BaseModel):
    username: str
    email: str

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class CategoryBase(BaseModel):
    name: str
    icon: Optional[str] = None
    color: Optional[str] = None
    description: Optional[str] = None

class Category(CategoryBase):
    id: int
    
    class Config:
        from_attributes = True

class ItemBase(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[dict] = None
    category_id: int
    priority: Optional[str] = "medium"

class ItemCreate(ItemBase):
    pass

class Item(ItemBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    status: str
    category: Optional[Category] = None
    
    class Config:
        from_attributes = True

class ReminderBase(BaseModel):
    remind_time: datetime
    title: str
    message: Optional[str] = None
    repeat_type: Optional[str] = "none"
    repeat_interval: Optional[int] = 1

class ReminderCreate(ReminderBase):
    item_id: int

class Reminder(ReminderBase):
    id: int
    item_id: int
    user_id: int
    is_sent: bool
    is_read: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class AIAnalysisRequest(BaseModel):
    text: str
    user_timezone: Optional[str] = "Asia/Shanghai"

class AIAnalysisResponse(BaseModel):
    category: str
    title: str
    description: str
    content: Optional[dict] = None
    reminders: List[dict] = []
    confidence: float