#!/usr/bin/env python3

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.core.config import settings

def migrate_task_steps():
    """迁移task_steps表，添加新字段"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        # 开始事务
        trans = conn.begin()
        
        try:
            print("开始迁移task_steps表...")
            
            # 删除现有表
            conn.execute(text("DROP TABLE IF EXISTS task_steps CASCADE"))
            print("已删除旧的task_steps表")
            
            # 重新创建表
            create_table_sql = """
            CREATE TABLE task_steps (
                id SERIAL PRIMARY KEY,
                item_id INTEGER REFERENCES items(id),
                parent_step_id INTEGER REFERENCES task_steps(id),
                title VARCHAR,
                description TEXT,
                order_index INTEGER,
                level INTEGER DEFAULT 1,
                status VARCHAR(11) DEFAULT 'DRAFT',
                estimated_duration INTEGER,
                step_type VARCHAR DEFAULT 'execution',
                dependencies JSON DEFAULT '[]',
                step_metadata JSON DEFAULT '{}',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            
            conn.execute(text(create_table_sql))
            print("已创建新的task_steps表")
            
            # 创建索引
            conn.execute(text("CREATE INDEX idx_task_steps_item_id ON task_steps(item_id)"))
            conn.execute(text("CREATE INDEX idx_task_steps_parent_id ON task_steps(parent_step_id)"))
            conn.execute(text("CREATE INDEX idx_task_steps_level ON task_steps(level)"))
            print("已创建索引")
            
            # 提交事务
            trans.commit()
            print("✅ task_steps表迁移完成！")
            
        except Exception as e:
            # 回滚事务
            trans.rollback()
            print(f"❌ 迁移失败: {e}")
            raise

if __name__ == "__main__":
    migrate_task_steps()
