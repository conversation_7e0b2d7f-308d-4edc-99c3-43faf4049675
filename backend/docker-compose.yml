version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: aiagent
      POSTGRES_USER: betterplan
      POSTGRES_PASSWORD: plan123456
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  api:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    environment:
      DATABASE_URL: ************************************************/aiagent
      REDIS_URL: redis://redis:6379
    volumes:
      - ./app:/app/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:
  redis_data: