#!/bin/bash

# 智能AI Agent 后端开发环境激活脚本
echo "🚀 启动智能AI Agent后端开发环境..."

# 激活虚拟环境
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "❌ 虚拟环境不存在，请先运行: python -m venv venv"
    exit 1
fi

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 检查依赖
if [ -f "requirements.txt" ]; then
    echo "📦 检查依赖包..."
    pip install -r requirements.txt > /dev/null 2>&1
fi

# 显示环境信息
echo ""
echo "🐍 Python版本: $(python --version)"
echo "📍 Python路径: $(which python)"
echo "📂 工作目录: $(pwd)"
echo "🔧 可用命令:"
echo "   python main.py          - 启动API服务"
echo "   python init_db.py       - 初始化数据库"
echo "   uvicorn main:app --reload --host 0.0.0.0 --port 8000  - 开发模式启动"
echo ""
echo "💡 提示: 使用 'deactivate' 命令退出虚拟环境"
echo ""

# 启动新的shell会话，保持虚拟环境激活
exec $SHELL