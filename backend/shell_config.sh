# 智能AI Agent 后端开发环境配置
# 将以下内容添加到你的 ~/.zshrc 或 ~/.bashrc 文件中

# 创建快捷命令进入后端目录并激活虚拟环境
function cdbackend() {
    cd /Users/<USER>/repos/betterAi/backend
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        echo "🐍 虚拟环境已激活 - 智能AI Agent后端"
        echo "📍 当前目录: $(pwd)"
        echo "🔧 可用命令: python main.py | python init_db.py | uvicorn main:app --reload"
    else
        echo "❌ 虚拟环境不存在"
    fi
}

# 创建别名
alias backend="cdbackend"
alias aiagent="cdbackend"

# 自动激活函数 - 当进入backend目录时自动激活虚拟环境
function auto_activate_venv() {
    if [[ "$PWD" == *"/betterAi/backend"* ]] && [[ "$VIRTUAL_ENV" == "" ]]; then
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
            echo "🐍 自动激活虚拟环境"
        fi
    fi
}

# 将自动激活函数添加到目录变更钩子
if [[ "$SHELL" == *"zsh"* ]]; then
    # Zsh
    autoload -U add-zsh-hook
    add-zsh-hook chpwd auto_activate_venv
else
    # Bash
    export PROMPT_COMMAND="auto_activate_venv; $PROMPT_COMMAND"
fi

echo "✅ 智能AI Agent开发环境配置已加载"
echo "💡 使用 'backend' 或 'aiagent' 命令快速进入后端目录"