#!/bin/bash

echo "🚀 启动智能AI Agent系统..."

# 检查并启动PostgreSQL
if ! pgrep -f postgres > /dev/null; then
    echo "🗄️  启动PostgreSQL服务..."
    brew services start postgresql@14 || brew services start postgresql
    sleep 2
else
    echo "✅ PostgreSQL已运行"
fi

# 检查并启动Redis
if ! pgrep -f redis-server > /dev/null; then
    echo "📡 启动Redis服务..."
    brew services start redis
    sleep 1
else
    echo "✅ Redis已运行"
fi

# 检查数据库是否存在，不存在则创建
echo "🔍 检查数据库..."
if ! psql -U betterplan -d aiagent -c '\q' 2>/dev/null; then
    echo "📊 创建数据库..."
    createdb -U betterplan aiagent 2>/dev/null || echo "数据库可能已存在"
fi

# 启动后端API服务
echo "🔧 启动后端API服务..."
cd backend
source venv/bin/activate

# 初始化数据库（如果需要）
echo "📋 初始化数据库表..."
python init_db.py

# 启动API服务
python main.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 测试后端是否正常
if curl -s http://localhost:8000/ > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
fi

# 启动Flutter Web应用
echo "🎨 启动Flutter前端应用..."
cd flutter_app
flutter run -d chrome --web-port 3000 &
FLUTTER_PID=$!
cd ..

echo ""
echo "✅ 系统启动完成！"
echo "📱 前端应用: http://localhost:3000"
echo "🔗 后端API: http://localhost:8000"
echo "📚 API文档: http://localhost:8000/docs"
echo ""
echo "💡 使用说明:"
echo "   1. 在前端输入或语音说出任务，如：'明天下午3点打羽毛球'"
echo "   2. AI会自动分类并创建提醒"
echo "   3. 所有任务在首页统一展示"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FLUTTER_PID 2>/dev/null; echo "👋 服务已停止"; exit' INT
wait